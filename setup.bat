@echo off
echo ========================================
echo    MindEase Project Setup Script
echo ========================================
echo.

echo [1/4] Setting up Backend...
cd backend
echo Installing Node.js dependencies...
call npm install
if %errorlevel% neq 0 (
    echo Error: Failed to install backend dependencies
    pause
    exit /b 1
)
echo Backend dependencies installed successfully!
echo.

echo [2/4] Setting up Frontend...
cd ..\frontend
echo Installing Flutter dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to install frontend dependencies
    pause
    exit /b 1
)
echo Frontend dependencies installed successfully!
echo.

echo [3/4] Checking Flutter setup...
call flutter doctor
echo.

echo [4/4] Setup completed successfully!
echo.
echo ========================================
echo    Setup Instructions
echo ========================================
echo.
echo 1. Make sure MongoDB is running on your system
echo    - Download from: https://www.mongodb.com/try/download/community
echo    - Start MongoDB service
echo.
echo 2. Update environment variables in backend/.env:
echo    - Add your email credentials for notifications
echo    - Add your Stripe keys for payments
echo    - Add your Google Maps API key
echo    - Add your OpenAI API key for AI features
echo.
echo 3. To start the application:
echo    - Run: start_app.bat
echo.
echo ========================================
echo    Next Steps
echo ========================================
echo.
echo Backend will run on: http://localhost:5000
echo Frontend will run on: http://localhost:8080 (or available port)
echo.
echo Press any key to continue...
pause > nul
