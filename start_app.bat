@echo off
echo ========================================
echo    Starting MindEase Application
echo ========================================
echo.

echo Checking if MongoDB is running...
tasklist /FI "IMAGENAME eq mongod.exe" 2>NUL | find /I /N "mongod.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo MongoDB is running ✓
) else (
    echo MongoDB is not running ✗
    echo Please start MongoDB service first
    echo You can start it with: net start MongoDB
    pause
    exit /b 1
)
echo.

echo Starting Backend Server...
start "MindEase Backend" cmd /k "cd backend && npm run dev"
echo Backend starting on http://localhost:5000
echo.

echo Waiting for backend to initialize...
timeout /t 5 /nobreak > nul
echo.

echo Starting Frontend Application...
start "MindEase Frontend" cmd /k "cd frontend && flutter run -d chrome --web-port 8080"
echo Frontend starting on http://localhost:8080
echo.

echo ========================================
echo    MindEase is starting up!
echo ========================================
echo.
echo Backend API: http://localhost:5000
echo Frontend App: http://localhost:8080
echo.
echo Both applications are starting in separate windows.
echo You can close this window once both are running.
echo.
echo Press any key to exit...
pause > nul
