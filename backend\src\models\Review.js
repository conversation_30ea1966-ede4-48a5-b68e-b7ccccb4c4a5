const mongoose = require('mongoose');

const reviewSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  therapist: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Therapist',
    required: true
  },
  consultation: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Consultation',
    required: true
  },
  
  // Rating and Review
  rating: {
    type: Number,
    required: [true, 'Rating is required'],
    min: [1, 'Rating must be at least 1'],
    max: [5, 'Rating cannot exceed 5']
  },
  title: {
    type: String,
    required: [true, 'Review title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  comment: {
    type: String,
    required: [true, 'Review comment is required'],
    trim: true,
    maxlength: [1000, 'Comment cannot exceed 1000 characters']
  },
  
  // Detailed Ratings
  detailedRatings: {
    communication: {
      type: Number,
      min: 1,
      max: 5
    },
    professionalism: {
      type: Number,
      min: 1,
      max: 5
    },
    helpfulness: {
      type: Number,
      min: 1,
      max: 5
    },
    punctuality: {
      type: Number,
      min: 1,
      max: 5
    },
    environment: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  
  // Review Status
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'flagged'],
    default: 'pending'
  },
  
  // Moderation
  moderation: {
    moderatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    moderatedAt: Date,
    reason: String,
    notes: String
  },
  
  // Flags and Reports
  flags: [{
    flaggedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reason: {
      type: String,
      enum: ['inappropriate', 'spam', 'fake', 'offensive', 'other']
    },
    description: String,
    flaggedAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Helpfulness
  helpfulness: {
    helpful: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }],
    notHelpful: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }]
  },
  
  // Therapist Response
  therapistResponse: {
    comment: {
      type: String,
      maxlength: [500, 'Response cannot exceed 500 characters']
    },
    respondedAt: Date
  },
  
  // Verification
  isVerified: {
    type: Boolean,
    default: false
  },
  verificationMethod: {
    type: String,
    enum: ['consultation_confirmed', 'payment_verified', 'manual']
  },
  
  // Privacy
  isAnonymous: {
    type: Boolean,
    default: false
  },
  
  // Metadata
  source: {
    type: String,
    enum: ['mobile_app', 'web_app', 'email_survey'],
    default: 'mobile_app'
  }
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
reviewSchema.index({ therapist: 1, status: 1, createdAt: -1 });
reviewSchema.index({ user: 1, createdAt: -1 });
reviewSchema.index({ consultation: 1 }, { unique: true }); // One review per consultation
reviewSchema.index({ rating: 1 });
reviewSchema.index({ status: 1 });

// Virtual for overall helpfulness score
reviewSchema.virtual('helpfulnessScore').get(function() {
  const helpful = this.helpfulness.helpful.length;
  const notHelpful = this.helpfulness.notHelpful.length;
  const total = helpful + notHelpful;
  
  if (total === 0) return 0;
  return (helpful / total) * 100;
});

// Virtual for average detailed rating
reviewSchema.virtual('averageDetailedRating').get(function() {
  const ratings = this.detailedRatings;
  const values = Object.values(ratings).filter(rating => rating != null);
  
  if (values.length === 0) return this.rating;
  
  const sum = values.reduce((acc, rating) => acc + rating, 0);
  return parseFloat((sum / values.length).toFixed(1));
});

// Virtual for formatted date
reviewSchema.virtual('formattedDate').get(function() {
  return this.createdAt.toLocaleDateString();
});

// Method to mark as helpful
reviewSchema.methods.markAsHelpful = function(userId) {
  // Remove from not helpful if exists
  this.helpfulness.notHelpful = this.helpfulness.notHelpful.filter(
    id => id.toString() !== userId.toString()
  );
  
  // Add to helpful if not already there
  if (!this.helpfulness.helpful.includes(userId)) {
    this.helpfulness.helpful.push(userId);
  }
  
  return this.save();
};

// Method to mark as not helpful
reviewSchema.methods.markAsNotHelpful = function(userId) {
  // Remove from helpful if exists
  this.helpfulness.helpful = this.helpfulness.helpful.filter(
    id => id.toString() !== userId.toString()
  );
  
  // Add to not helpful if not already there
  if (!this.helpfulness.notHelpful.includes(userId)) {
    this.helpfulness.notHelpful.push(userId);
  }
  
  return this.save();
};

// Method to flag review
reviewSchema.methods.flagReview = function(userId, reason, description) {
  // Check if user already flagged this review
  const existingFlag = this.flags.find(
    flag => flag.flaggedBy.toString() === userId.toString()
  );
  
  if (existingFlag) {
    throw new Error('You have already flagged this review');
  }
  
  this.flags.push({
    flaggedBy: userId,
    reason,
    description,
    flaggedAt: new Date()
  });
  
  // Auto-flag if multiple flags
  if (this.flags.length >= 3) {
    this.status = 'flagged';
  }
  
  return this.save();
};

// Method to approve review
reviewSchema.methods.approve = function(moderatorId, notes) {
  this.status = 'approved';
  this.moderation = {
    moderatedBy: moderatorId,
    moderatedAt: new Date(),
    notes
  };
  
  return this.save();
};

// Method to reject review
reviewSchema.methods.reject = function(moderatorId, reason, notes) {
  this.status = 'rejected';
  this.moderation = {
    moderatedBy: moderatorId,
    moderatedAt: new Date(),
    reason,
    notes
  };
  
  return this.save();
};

// Static method to get review statistics for a therapist
reviewSchema.statics.getTherapistStats = function(therapistId) {
  return this.aggregate([
    {
      $match: {
        therapist: mongoose.Types.ObjectId(therapistId),
        status: 'approved'
      }
    },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalReviews: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        },
        averageDetailedRatings: {
          $push: '$detailedRatings'
        }
      }
    }
  ]);
};

// Static method to get recent reviews
reviewSchema.statics.getRecentReviews = function(limit = 10) {
  return this.find({ status: 'approved' })
    .populate('user', 'firstName lastName avatar')
    .populate('therapist')
    .sort({ createdAt: -1 })
    .limit(limit);
};

// Pre-save middleware to auto-verify reviews
reviewSchema.pre('save', async function(next) {
  if (this.isNew) {
    try {
      // Check if consultation exists and is completed
      const Consultation = mongoose.model('Consultation');
      const consultation = await Consultation.findById(this.consultation);
      
      if (consultation && consultation.status === 'completed') {
        this.isVerified = true;
        this.verificationMethod = 'consultation_confirmed';
        this.status = 'approved'; // Auto-approve verified reviews
      }
    } catch (error) {
      console.error('Error verifying review:', error);
    }
  }
  next();
});

// Post-save middleware to update therapist rating
reviewSchema.post('save', async function(doc) {
  if (doc.status === 'approved') {
    try {
      const Therapist = mongoose.model('Therapist');
      await Therapist.findByIdAndUpdate(doc.therapist, {}, { 
        runValidators: false 
      });
    } catch (error) {
      console.error('Error updating therapist rating:', error);
    }
  }
});

module.exports = mongoose.model('Review', reviewSchema);
