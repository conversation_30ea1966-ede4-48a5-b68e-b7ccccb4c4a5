import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/app_theme.dart';

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Onboarding Screen - Coming Soon'),
            const Sized<PERSON><PERSON>(height: 20),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Skip to Home'),
            ),
          ],
        ),
      ),
    );
  }
}
