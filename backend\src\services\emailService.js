const nodemailer = require('nodemailer');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
  }

  async sendWelcomeEmail(user) {
    const mailOptions = {
      from: `MindEase <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: 'Welcome to MindEase!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6B73FF;">Welcome to MindEase, ${user.firstName}!</h1>
          <p>We're excited to have you join our mental health community.</p>
          <p>Your journey to better mental wellness starts here.</p>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3>What you can do with MindEase:</h3>
            <ul>
              <li>Track your daily mood and emotions</li>
              <li>Get AI-powered insights and recommendations</li>
              <li>Connect with certified therapists</li>
              <li>Schedule online consultations</li>
              <li>Access crisis support when needed</li>
            </ul>
          </div>
          <p>If you have any questions, our support team is here to help.</p>
          <p>Best regards,<br>The MindEase Team</p>
        </div>
      `
    };

    return await this.transporter.sendMail(mailOptions);
  }

  async sendAppointmentReminder(user, consultation) {
    const appointmentDate = new Date(consultation.scheduledDate);
    const mailOptions = {
      from: `MindEase <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: 'Appointment Reminder - MindEase',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6B73FF;">Appointment Reminder</h1>
          <p>Hi ${user.firstName},</p>
          <p>This is a reminder about your upcoming therapy session:</p>
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Date:</strong> ${appointmentDate.toLocaleDateString()}</p>
            <p><strong>Time:</strong> ${appointmentDate.toLocaleTimeString()}</p>
            <p><strong>Type:</strong> ${consultation.type}</p>
            <p><strong>Duration:</strong> ${consultation.duration} minutes</p>
          </div>
          <p>Please make sure you're ready 5 minutes before your session starts.</p>
          <p>If you need to reschedule, please do so at least 24 hours in advance.</p>
          <p>Best regards,<br>The MindEase Team</p>
        </div>
      `
    };

    return await this.transporter.sendMail(mailOptions);
  }

  async sendPasswordResetEmail(user, resetUrl) {
    const mailOptions = {
      from: `MindEase <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: 'Password Reset - MindEase',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #6B73FF;">Password Reset Request</h1>
          <p>Hi ${user.firstName},</p>
          <p>You requested to reset your password. Click the button below to reset it:</p>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" style="background: #6B73FF; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
          </div>
          <p>This link will expire in 10 minutes for security reasons.</p>
          <p>If you didn't request this, please ignore this email.</p>
          <p>Best regards,<br>The MindEase Team</p>
        </div>
      `
    };

    return await this.transporter.sendMail(mailOptions);
  }

  async sendCrisisAlert(user) {
    const mailOptions = {
      from: `MindEase <${process.env.EMAIL_USER}>`,
      to: user.email,
      subject: 'Crisis Support Resources - MindEase',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #EF4444;">Crisis Support Resources</h1>
          <p>Hi ${user.firstName},</p>
          <p>We noticed you might be going through a difficult time. Please know that help is available.</p>
          <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #dc2626;">Immediate Help:</h3>
            <p><strong>National Suicide Prevention Lifeline:</strong> 988</p>
            <p><strong>Crisis Text Line:</strong> Text HOME to 741741</p>
            <p><strong>Emergency Services:</strong> 911</p>
          </div>
          <p>You are not alone. Professional help is available 24/7.</p>
          <p>Please reach out to someone you trust or contact emergency services if you're in immediate danger.</p>
          <p>With care,<br>The MindEase Team</p>
        </div>
      `
    };

    return await this.transporter.sendMail(mailOptions);
  }
}

module.exports = new EmailService();
