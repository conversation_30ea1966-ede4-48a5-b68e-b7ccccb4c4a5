const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Payment Details
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0, 'Amount cannot be negative']
  },
  currency: {
    type: String,
    default: 'USD',
    uppercase: true
  },
  
  // Payment Status
  status: {
    type: String,
    enum: ['pending', 'processing', 'succeeded', 'failed', 'cancelled', 'refunded', 'partially_refunded'],
    default: 'pending'
  },
  
  // Payment Method
  paymentMethod: {
    type: {
      type: String,
      enum: ['card', 'bank_transfer', 'paypal', 'apple_pay', 'google_pay'],
      required: true
    },
    last4: String, // Last 4 digits for cards
    brand: String, // Visa, Mastercard, etc.
    expiryMonth: Number,
    expiryYear: Number
  },
  
  // Payment Gateway Information
  gateway: {
    provider: {
      type: String,
      enum: ['stripe', 'paypal', 'square'],
      default: 'stripe'
    },
    transactionId: {
      type: String,
      required: true,
      unique: true
    },
    paymentIntentId: String,
    chargeId: String,
    receiptUrl: String
  },
  
  // Related Services
  relatedTo: {
    type: {
      type: String,
      enum: ['consultation', 'subscription', 'premium_feature'],
      required: true
    },
    id: {
      type: mongoose.Schema.Types.ObjectId,
      required: true
    }
  },
  
  // Billing Information
  billing: {
    name: String,
    email: String,
    address: {
      line1: String,
      line2: String,
      city: String,
      state: String,
      postalCode: String,
      country: String
    }
  },
  
  // Refund Information
  refund: {
    amount: {
      type: Number,
      default: 0
    },
    reason: String,
    refundedAt: Date,
    refundId: String,
    status: {
      type: String,
      enum: ['pending', 'succeeded', 'failed', 'cancelled']
    }
  },
  
  // Fees and Taxes
  fees: {
    platformFee: {
      type: Number,
      default: 0
    },
    processingFee: {
      type: Number,
      default: 0
    },
    tax: {
      type: Number,
      default: 0
    },
    total: {
      type: Number,
      default: 0
    }
  },
  
  // Metadata
  metadata: {
    type: Map,
    of: String
  },
  
  // Timestamps
  paidAt: Date,
  failedAt: Date,
  cancelledAt: Date,
  
  // Failure Information
  failure: {
    code: String,
    message: String,
    declineCode: String
  },
  
  // Invoice Information
  invoice: {
    number: String,
    url: String,
    pdfUrl: String
  },
  
  // Subscription specific (if applicable)
  subscription: {
    plan: String,
    period: {
      start: Date,
      end: Date
    },
    isRecurring: {
      type: Boolean,
      default: false
    }
  }
  
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for efficient queries
paymentSchema.index({ user: 1, createdAt: -1 });
paymentSchema.index({ status: 1 });
paymentSchema.index({ 'gateway.transactionId': 1 });
paymentSchema.index({ 'relatedTo.type': 1, 'relatedTo.id': 1 });

// Virtual for net amount (amount - fees)
paymentSchema.virtual('netAmount').get(function() {
  return this.amount - (this.fees.total || 0);
});

// Virtual for formatted amount
paymentSchema.virtual('formattedAmount').get(function() {
  return `${this.currency} ${this.amount.toFixed(2)}`;
});

// Virtual for payment description
paymentSchema.virtual('description').get(function() {
  const typeDescriptions = {
    consultation: 'Therapy Consultation',
    subscription: 'Premium Subscription',
    premium_feature: 'Premium Feature'
  };
  return typeDescriptions[this.relatedTo.type] || 'Payment';
});

// Method to check if payment can be refunded
paymentSchema.methods.canBeRefunded = function() {
  const refundableStatuses = ['succeeded'];
  const maxRefundDays = 30; // 30 days refund policy
  
  if (!refundableStatuses.includes(this.status)) {
    return false;
  }
  
  if (!this.paidAt) {
    return false;
  }
  
  const daysSincePayment = (new Date() - this.paidAt) / (1000 * 60 * 60 * 24);
  return daysSincePayment <= maxRefundDays;
};

// Method to calculate refund amount
paymentSchema.methods.calculateRefundAmount = function(requestedAmount) {
  const maxRefundable = this.amount - (this.refund.amount || 0);
  
  if (requestedAmount && requestedAmount <= maxRefundable) {
    return requestedAmount;
  }
  
  return maxRefundable;
};

// Method to mark payment as succeeded
paymentSchema.methods.markAsSucceeded = function(paymentData = {}) {
  this.status = 'succeeded';
  this.paidAt = new Date();
  
  if (paymentData.chargeId) {
    this.gateway.chargeId = paymentData.chargeId;
  }
  
  if (paymentData.receiptUrl) {
    this.gateway.receiptUrl = paymentData.receiptUrl;
  }
  
  return this.save();
};

// Method to mark payment as failed
paymentSchema.methods.markAsFailed = function(failureData = {}) {
  this.status = 'failed';
  this.failedAt = new Date();
  
  if (failureData.code || failureData.message || failureData.declineCode) {
    this.failure = {
      code: failureData.code,
      message: failureData.message,
      declineCode: failureData.declineCode
    };
  }
  
  return this.save();
};

// Static method to get payment statistics
paymentSchema.statics.getStats = function(startDate, endDate, filters = {}) {
  const matchStage = {
    createdAt: {
      $gte: startDate,
      $lte: endDate
    },
    ...filters
  };
  
  return this.aggregate([
    { $match: matchStage },
    {
      $group: {
        _id: null,
        totalAmount: { $sum: '$amount' },
        totalCount: { $sum: 1 },
        succeededAmount: {
          $sum: {
            $cond: [{ $eq: ['$status', 'succeeded'] }, '$amount', 0]
          }
        },
        succeededCount: {
          $sum: {
            $cond: [{ $eq: ['$status', 'succeeded'] }, 1, 0]
          }
        },
        failedCount: {
          $sum: {
            $cond: [{ $eq: ['$status', 'failed'] }, 1, 0]
          }
        },
        refundedAmount: { $sum: '$refund.amount' }
      }
    }
  ]);
};

// Pre-save middleware to generate invoice number
paymentSchema.pre('save', function(next) {
  if (this.isNew && !this.invoice.number) {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    this.invoice.number = `INV-${timestamp}-${random}`;
  }
  next();
});

module.exports = mongoose.model('Payment', paymentSchema);
