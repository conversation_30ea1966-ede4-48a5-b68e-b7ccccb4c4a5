const express = require('express');
const { body, validationResult } = require('express-validator');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Payment = require('../models/Payment');
const Consultation = require('../models/Consultation');
const { protect } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// @desc    Create payment intent
// @route   POST /api/payments/create-intent
// @access  Private
router.post('/create-intent', [
  body('amount').isFloat({ min: 0.5 }).withMessage('Amount must be at least $0.50'),
  body('currency').optional().isIn(['USD', 'EUR', 'GBP']).withMessage('Invalid currency'),
  body('relatedTo.type').isIn(['consultation', 'subscription', 'premium_feature']).withMessage('Invalid payment type'),
  body('relatedTo.id').isMongoId().withMessage('Valid related ID is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { amount, currency = 'USD', relatedTo, metadata = {} } = req.body;

    // Validate the related entity exists
    if (relatedTo.type === 'consultation') {
      const consultation = await Consultation.findById(relatedTo.id);
      if (!consultation) {
        return res.status(404).json({
          success: false,
          message: 'Consultation not found'
        });
      }
      
      // Check if user owns this consultation
      if (consultation.patient.toString() !== req.user._id.toString()) {
        return res.status(403).json({
          success: false,
          message: 'Access denied'
        });
      }
    }

    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: currency.toLowerCase(),
      metadata: {
        userId: req.user._id.toString(),
        relatedType: relatedTo.type,
        relatedId: relatedTo.id,
        ...metadata
      },
      automatic_payment_methods: {
        enabled: true
      }
    });

    // Create payment record
    const payment = await Payment.create({
      user: req.user._id,
      amount,
      currency: currency.toUpperCase(),
      status: 'pending',
      paymentMethod: {
        type: 'card' // Default, will be updated when payment is completed
      },
      gateway: {
        provider: 'stripe',
        transactionId: paymentIntent.id,
        paymentIntentId: paymentIntent.id
      },
      relatedTo,
      billing: {
        name: `${req.user.firstName} ${req.user.lastName}`,
        email: req.user.email
      },
      metadata
    });

    res.status(201).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentId: payment._id,
      amount,
      currency
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Confirm payment
// @route   POST /api/payments/:id/confirm
// @access  Private
router.post('/:id/confirm', async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if user owns this payment
    if (payment.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Retrieve payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(payment.gateway.paymentIntentId);

    if (paymentIntent.status === 'succeeded') {
      await payment.markAsSucceeded({
        chargeId: paymentIntent.latest_charge,
        receiptUrl: paymentIntent.charges?.data[0]?.receipt_url
      });

      // Update related entity if needed
      if (payment.relatedTo.type === 'consultation') {
        await Consultation.findByIdAndUpdate(payment.relatedTo.id, {
          'payment.paymentId': payment._id,
          'payment.paymentStatus': 'paid',
          'payment.paidAt': new Date()
        });
      }

      res.status(200).json({
        success: true,
        message: 'Payment confirmed successfully',
        payment
      });
    } else {
      res.status(400).json({
        success: false,
        message: 'Payment not completed',
        status: paymentIntent.status
      });
    }
  } catch (error) {
    next(error);
  }
});

// @desc    Get user's payments
// @route   GET /api/payments
// @access  Private
router.get('/', async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const query = { user: req.user._id };

    if (req.query.status) {
      query.status = req.query.status;
    }

    if (req.query.type) {
      query['relatedTo.type'] = req.query.type;
    }

    const payments = await Payment.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('relatedTo.id');

    const total = await Payment.countDocuments(query);

    res.status(200).json({
      success: true,
      count: payments.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      payments
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get payment by ID
// @route   GET /api/payments/:id
// @access  Private
router.get('/:id', async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate('user', 'firstName lastName email')
      .populate('relatedTo.id');

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if user owns this payment or is admin
    if (payment.user._id.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.status(200).json({
      success: true,
      payment
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Request refund
// @route   POST /api/payments/:id/refund
// @access  Private
router.post('/:id/refund', [
  body('amount').optional().isFloat({ min: 0.01 }).withMessage('Refund amount must be positive'),
  body('reason').notEmpty().withMessage('Refund reason is required')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const payment = await Payment.findById(req.params.id);

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if user owns this payment
    if (payment.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    if (!payment.canBeRefunded()) {
      return res.status(400).json({
        success: false,
        message: 'Payment cannot be refunded'
      });
    }

    const refundAmount = payment.calculateRefundAmount(req.body.amount);

    if (refundAmount <= 0) {
      return res.status(400).json({
        success: false,
        message: 'No refundable amount available'
      });
    }

    // Create refund in Stripe
    const refund = await stripe.refunds.create({
      payment_intent: payment.gateway.paymentIntentId,
      amount: Math.round(refundAmount * 100), // Convert to cents
      reason: 'requested_by_customer',
      metadata: {
        reason: req.body.reason
      }
    });

    // Update payment record
    payment.refund = {
      amount: refundAmount,
      reason: req.body.reason,
      refundedAt: new Date(),
      refundId: refund.id,
      status: refund.status
    };

    if (refundAmount === payment.amount) {
      payment.status = 'refunded';
    } else {
      payment.status = 'partially_refunded';
    }

    await payment.save();

    res.status(200).json({
      success: true,
      message: 'Refund processed successfully',
      refund: payment.refund
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Stripe webhook handler
// @route   POST /api/payments/webhook
// @access  Public (Stripe webhook)
router.post('/webhook', express.raw({ type: 'application/json' }), async (req, res, next) => {
  try {
    const sig = req.headers['stripe-signature'];
    let event;

    try {
      event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        const paymentIntent = event.data.object;
        await handlePaymentSucceeded(paymentIntent);
        break;
      
      case 'payment_intent.payment_failed':
        const failedPayment = event.data.object;
        await handlePaymentFailed(failedPayment);
        break;
      
      case 'charge.dispute.created':
        const dispute = event.data.object;
        await handleChargeDispute(dispute);
        break;
      
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
});

// Helper function to handle successful payments
async function handlePaymentSucceeded(paymentIntent) {
  try {
    const payment = await Payment.findOne({
      'gateway.paymentIntentId': paymentIntent.id
    });

    if (payment && payment.status !== 'succeeded') {
      await payment.markAsSucceeded({
        chargeId: paymentIntent.latest_charge,
        receiptUrl: paymentIntent.charges?.data[0]?.receipt_url
      });

      // Update related consultation
      if (payment.relatedTo.type === 'consultation') {
        await Consultation.findByIdAndUpdate(payment.relatedTo.id, {
          'payment.paymentStatus': 'paid',
          'payment.paidAt': new Date(),
          status: 'confirmed'
        });
      }
    }
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

// Helper function to handle failed payments
async function handlePaymentFailed(paymentIntent) {
  try {
    const payment = await Payment.findOne({
      'gateway.paymentIntentId': paymentIntent.id
    });

    if (payment) {
      await payment.markAsFailed({
        code: paymentIntent.last_payment_error?.code,
        message: paymentIntent.last_payment_error?.message,
        declineCode: paymentIntent.last_payment_error?.decline_code
      });
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

// Helper function to handle charge disputes
async function handleChargeDispute(dispute) {
  try {
    const payment = await Payment.findOne({
      'gateway.chargeId': dispute.charge
    });

    if (payment) {
      // Handle dispute logic here
      console.log(`Dispute created for payment ${payment._id}`);
    }
  } catch (error) {
    console.error('Error handling charge dispute:', error);
  }
}

module.exports = router;
