import 'package:flutter/foundation.dart';

class AppConfig {
  static const String _devBaseUrl = 'http://localhost:5000/api';
  static const String _prodBaseUrl = 'https://api.mindease.app/api';
  
  static const String _devSocketUrl = 'http://localhost:5000';
  static const String _prodSocketUrl = 'https://api.mindease.app';
  
  // Environment Configuration
  static bool get isDebug => kDebugMode;
  static bool get isRelease => kReleaseMode;
  static bool get isProfile => kProfileMode;
  
  // API Configuration
  static String get baseUrl => isDebug ? _devBaseUrl : _prodBaseUrl;
  static String get socketUrl => isDebug ? _devSocketUrl : _prodSocketUrl;
  
  // Timeout Configuration
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Cache Configuration
  static const Duration cacheMaxAge = Duration(hours: 1);
  static const int maxCacheSize = 50 * 1024 * 1024; // 50MB
  
  // Logging Configuration
  static bool get enableLogging => isDebug;
  static bool get enableNetworkLogging => isDebug;
  static bool get enableCrashlytics => isRelease;
  
  // Feature Flags
  static bool get enableBiometricAuth => true;
  static bool get enablePushNotifications => true;
  static bool get enableVideoCall => true;
  static bool get enablePayments => true;
  static bool get enableAnalytics => isRelease;
  
  // Security Configuration
  static const String jwtSecretKey = 'your_jwt_secret_key';
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  
  // Pagination Configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload Configuration
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const int maxVideoSize = 50 * 1024 * 1024; // 50MB
  static const List<String> allowedImageFormats = ['jpg', 'jpeg', 'png', 'webp'];
  static const List<String> allowedVideoFormats = ['mp4', 'mov', 'avi'];
  
  // Notification Configuration
  static const String fcmServerKey = 'your_fcm_server_key';
  static const String vapidKey = 'your_vapid_key';
  
  // Payment Configuration
  static const String stripePublishableKey = isDebug 
    ? 'pk_test_your_stripe_test_key'
    : 'pk_live_your_stripe_live_key';
  
  // Maps Configuration
  static const String googleMapsApiKey = 'your_google_maps_api_key';
  
  // Social Login Configuration
  static const String googleClientId = 'your_google_client_id';
  static const String facebookAppId = 'your_facebook_app_id';
  static const String appleClientId = 'your_apple_client_id';
  
  // Analytics Configuration
  static const String firebaseProjectId = 'mindease-app';
  static const String mixpanelToken = 'your_mixpanel_token';
  
  // Video Call Configuration
  static const String agoraAppId = 'your_agora_app_id';
  static const String agoraAppCertificate = 'your_agora_app_certificate';
  
  // Error Reporting Configuration
  static const String sentryDsn = 'your_sentry_dsn';
  
  // App Store Configuration
  static const String appStoreId = 'your_app_store_id';
  static const String playStoreId = 'com.mindease.app';
  
  // Deep Link Configuration
  static const String deepLinkScheme = 'mindease';
  static const String universalLinkDomain = 'mindease.app';
  
  // Encryption Configuration
  static const String encryptionKey = 'your_encryption_key';
  static const String encryptionIv = 'your_encryption_iv';
  
  // Rate Limiting Configuration
  static const int maxRequestsPerMinute = 60;
  static const Duration rateLimitWindow = Duration(minutes: 1);
  
  // Offline Configuration
  static const Duration offlineRetryInterval = Duration(seconds: 30);
  static const int maxOfflineRetries = 3;
  
  // Background Task Configuration
  static const Duration backgroundSyncInterval = Duration(minutes: 15);
  static const Duration backgroundTaskTimeout = Duration(minutes: 1);
  
  // Mood Tracking Configuration
  static const int maxMoodEntriesPerDay = 10;
  static const Duration moodReminderInterval = Duration(hours: 8);
  
  // AI Configuration
  static const String openAiApiKey = 'your_openai_api_key';
  static const String openAiModel = 'gpt-3.5-turbo';
  static const int maxAiTokens = 1000;
  
  // Consultation Configuration
  static const Duration consultationReminderTime = Duration(hours: 1);
  static const Duration consultationCancellationWindow = Duration(hours: 24);
  static const Duration consultationRescheduleWindow = Duration(hours: 48);
  
  // Review Configuration
  static const Duration reviewReminderDelay = Duration(hours: 24);
  static const int maxReviewLength = 1000;
  static const int minReviewLength = 10;
  
  // Subscription Configuration
  static const Duration trialPeriod = Duration(days: 7);
  static const Duration subscriptionReminderTime = Duration(days: 3);
  
  // Privacy Configuration
  static const Duration dataRetentionPeriod = Duration(days: 365);
  static const Duration inactiveAccountDeletionPeriod = Duration(days: 730);
  
  // Accessibility Configuration
  static const double minFontSize = 12.0;
  static const double maxFontSize = 24.0;
  static const double defaultFontSize = 16.0;
  
  // Performance Configuration
  static const int maxConcurrentRequests = 5;
  static const Duration requestTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  
  // Localization Configuration
  static const List<String> supportedLanguages = ['en', 'es', 'fr', 'de'];
  static const String defaultLanguage = 'en';
  
  // Theme Configuration
  static const String defaultTheme = 'system';
  static const List<String> availableThemes = ['light', 'dark', 'system'];
}
