const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Consultation = require('../models/Consultation');
const Therapist = require('../models/Therapist');
const { protect, authorize } = require('../middleware/auth');
const { v4: uuidv4 } = require('uuid');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// @desc    Book a consultation
// @route   POST /api/consultations
// @access  Private (User)
router.post('/', authorize('user'), [
  body('therapist').isMongoId().withMessage('Valid therapist ID is required'),
  body('scheduledDate').isISO8601().withMessage('Valid scheduled date is required'),
  body('duration').isInt({ min: 30, max: 120 }).withMessage('Duration must be between 30 and 120 minutes'),
  body('type').isIn(['video', 'audio', 'in-person', 'chat']).withMessage('Invalid consultation type')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { therapist: therapistId, scheduledDate, duration, type } = req.body;

    // Check if therapist exists and is available
    const therapist = await Therapist.findById(therapistId).populate('user');
    if (!therapist) {
      return res.status(404).json({
        success: false,
        message: 'Therapist not found'
      });
    }

    if (!therapist.isActive || !therapist.isVerified || !therapist.isAcceptingNewClients) {
      return res.status(400).json({
        success: false,
        message: 'Therapist is not available for bookings'
      });
    }

    // Check if the scheduled time is in the future
    const appointmentDate = new Date(scheduledDate);
    if (appointmentDate <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'Appointment must be scheduled for a future date and time'
      });
    }

    // Check for conflicting appointments
    const conflictingAppointment = await Consultation.findOne({
      therapist: therapistId,
      scheduledDate: {
        $gte: new Date(appointmentDate.getTime() - duration * 60 * 1000),
        $lte: new Date(appointmentDate.getTime() + duration * 60 * 1000)
      },
      status: { $in: ['scheduled', 'confirmed', 'in-progress'] }
    });

    if (conflictingAppointment) {
      return res.status(400).json({
        success: false,
        message: 'Therapist is not available at the requested time'
      });
    }

    // Find the service price
    const service = therapist.services.find(s => s.duration === duration);
    const basePrice = service ? service.price : 100; // Default price if service not found

    // Generate room ID for online sessions
    const roomId = type !== 'in-person' ? uuidv4() : null;
    const meetingLink = roomId ? `${process.env.FRONTEND_URL}/consultation/${roomId}` : null;

    const consultationData = {
      patient: req.user._id,
      therapist: therapistId,
      scheduledDate: appointmentDate,
      duration,
      type,
      pricing: {
        basePrice,
        discount: 0,
        finalPrice: basePrice
      },
      sessionDetails: {
        roomId,
        meetingLink
      },
      createdBy: req.user._id
    };

    const consultation = await Consultation.create(consultationData);
    await consultation.populate([
      { path: 'patient', select: 'firstName lastName email phone' },
      { path: 'therapist', populate: { path: 'user', select: 'firstName lastName email' } }
    ]);

    res.status(201).json({
      success: true,
      message: 'Consultation booked successfully',
      consultation
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user's consultations
// @route   GET /api/consultations
// @access  Private
router.get('/', [
  query('status').optional().isIn(['scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'no-show']).withMessage('Invalid status'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build query based on user role
    let query = {};
    if (req.user.role === 'user') {
      query.patient = req.user._id;
    } else if (req.user.role === 'therapist') {
      // Find therapist profile
      const therapist = await Therapist.findOne({ user: req.user._id });
      if (therapist) {
        query.therapist = therapist._id;
      }
    }

    if (req.query.status) {
      query.status = req.query.status;
    }

    const consultations = await Consultation.find(query)
      .populate('patient', 'firstName lastName email phone')
      .populate({
        path: 'therapist',
        populate: { path: 'user', select: 'firstName lastName email' }
      })
      .sort({ scheduledDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Consultation.countDocuments(query);

    res.status(200).json({
      success: true,
      count: consultations.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      consultations
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get consultation by ID
// @route   GET /api/consultations/:id
// @access  Private
router.get('/:id', async (req, res, next) => {
  try {
    const consultation = await Consultation.findById(req.params.id)
      .populate('patient', 'firstName lastName email phone')
      .populate({
        path: 'therapist',
        populate: { path: 'user', select: 'firstName lastName email' }
      });

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found'
      });
    }

    // Check if user has access to this consultation
    const hasAccess = 
      consultation.patient._id.toString() === req.user._id.toString() ||
      (consultation.therapist.user && consultation.therapist.user._id.toString() === req.user._id.toString()) ||
      req.user.role === 'admin';

    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.status(200).json({
      success: true,
      consultation
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update consultation status
// @route   PUT /api/consultations/:id/status
// @access  Private
router.put('/:id/status', [
  body('status').isIn(['confirmed', 'in-progress', 'completed', 'cancelled', 'no-show']).withMessage('Invalid status'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const consultation = await Consultation.findById(req.params.id);

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found'
      });
    }

    // Check if user has permission to update status
    const hasPermission = 
      consultation.patient.toString() === req.user._id.toString() ||
      req.user.role === 'therapist' ||
      req.user.role === 'admin';

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    const { status, reason } = req.body;

    // Handle cancellation
    if (status === 'cancelled') {
      consultation.cancellation = {
        cancelledBy: req.user.role,
        cancelledAt: new Date(),
        reason: reason || 'No reason provided',
        refundEligible: consultation.canBeCancelled()
      };
    }

    consultation.status = status;
    consultation.lastModifiedBy = req.user._id;
    await consultation.save();

    res.status(200).json({
      success: true,
      message: 'Consultation status updated successfully',
      consultation
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Reschedule consultation
// @route   PUT /api/consultations/:id/reschedule
// @access  Private
router.put('/:id/reschedule', [
  body('newDate').isISO8601().withMessage('Valid new date is required'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const consultation = await Consultation.findById(req.params.id);

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found'
      });
    }

    // Check if user has permission to reschedule
    const hasPermission = 
      consultation.patient.toString() === req.user._id.toString() ||
      req.user.role === 'therapist';

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    if (!consultation.canBeRescheduled()) {
      return res.status(400).json({
        success: false,
        message: 'Consultation cannot be rescheduled at this time'
      });
    }

    const newDate = new Date(req.body.newDate);
    if (newDate <= new Date()) {
      return res.status(400).json({
        success: false,
        message: 'New date must be in the future'
      });
    }

    // Check for conflicts at new time
    const conflictingAppointment = await Consultation.findOne({
      therapist: consultation.therapist,
      scheduledDate: {
        $gte: new Date(newDate.getTime() - consultation.duration * 60 * 1000),
        $lte: new Date(newDate.getTime() + consultation.duration * 60 * 1000)
      },
      status: { $in: ['scheduled', 'confirmed', 'in-progress'] },
      _id: { $ne: consultation._id }
    });

    if (conflictingAppointment) {
      return res.status(400).json({
        success: false,
        message: 'Therapist is not available at the new requested time'
      });
    }

    // Update consultation
    consultation.rescheduling = {
      originalDate: consultation.scheduledDate,
      rescheduledBy: req.user.role,
      rescheduledAt: new Date(),
      reason: req.body.reason || 'No reason provided'
    };

    consultation.scheduledDate = newDate;
    consultation.lastModifiedBy = req.user._id;
    await consultation.save();

    res.status(200).json({
      success: true,
      message: 'Consultation rescheduled successfully',
      consultation
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Add session notes (Therapist only)
// @route   PUT /api/consultations/:id/notes
// @access  Private (Therapist)
router.put('/:id/notes', authorize('therapist'), [
  body('therapistNotes').optional().isLength({ max: 2000 }).withMessage('Notes cannot exceed 2000 characters'),
  body('goals').optional().isArray().withMessage('Goals must be an array'),
  body('homework').optional().isArray().withMessage('Homework must be an array'),
  body('nextSteps').optional().isString().withMessage('Next steps must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const consultation = await Consultation.findById(req.params.id);

    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found'
      });
    }

    // Check if therapist owns this consultation
    const therapist = await Therapist.findOne({ user: req.user._id });
    if (!therapist || consultation.therapist.toString() !== therapist._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    // Update session notes
    consultation.sessionNotes = {
      ...consultation.sessionNotes,
      ...req.body
    };

    consultation.lastModifiedBy = req.user._id;
    await consultation.save();

    res.status(200).json({
      success: true,
      message: 'Session notes updated successfully',
      sessionNotes: consultation.sessionNotes
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get upcoming consultations
// @route   GET /api/consultations/upcoming
// @access  Private
router.get('/upcoming', async (req, res, next) => {
  try {
    let consultations;

    if (req.user.role === 'user') {
      consultations = await Consultation.getUpcoming(req.user._id, 'patient');
    } else if (req.user.role === 'therapist') {
      const therapist = await Therapist.findOne({ user: req.user._id });
      if (therapist) {
        consultations = await Consultation.getUpcoming(therapist._id, 'therapist');
      } else {
        consultations = [];
      }
    }

    res.status(200).json({
      success: true,
      count: consultations.length,
      consultations
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
