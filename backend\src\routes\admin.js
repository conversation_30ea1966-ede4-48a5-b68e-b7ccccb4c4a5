const express = require('express');
const { body, validationResult, query } = require('express-validator');
const User = require('../models/User');
const Therapist = require('../models/Therapist');
const Consultation = require('../models/Consultation');
const Payment = require('../models/Payment');
const Review = require('../models/Review');
const Mood = require('../models/Mood');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Apply authentication and admin authorization to all routes
router.use(protect);
router.use(authorize('admin'));

// @desc    Get admin dashboard statistics
// @route   GET /api/admin/dashboard
// @access  Private (Admin only)
router.get('/dashboard', async (req, res, next) => {
  try {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // User statistics
    const totalUsers = await User.countDocuments({ role: 'user' });
    const activeUsers = await User.countDocuments({ role: 'user', isActive: true });
    const newUsersThisMonth = await User.countDocuments({
      role: 'user',
      createdAt: { $gte: thirtyDaysAgo }
    });

    // Therapist statistics
    const totalTherapists = await Therapist.countDocuments();
    const verifiedTherapists = await Therapist.countDocuments({ isVerified: true });
    const activeTherapists = await Therapist.countDocuments({ isActive: true });

    // Consultation statistics
    const totalConsultations = await Consultation.countDocuments();
    const completedConsultations = await Consultation.countDocuments({ status: 'completed' });
    const upcomingConsultations = await Consultation.countDocuments({
      status: { $in: ['scheduled', 'confirmed'] },
      scheduledDate: { $gte: now }
    });

    // Payment statistics
    const paymentStats = await Payment.getStats(thirtyDaysAgo, now);
    const totalRevenue = paymentStats[0]?.succeededAmount || 0;
    const totalTransactions = paymentStats[0]?.succeededCount || 0;

    // Review statistics
    const totalReviews = await Review.countDocuments({ status: 'approved' });
    const pendingReviews = await Review.countDocuments({ status: 'pending' });
    const averageRating = await Review.aggregate([
      { $match: { status: 'approved' } },
      { $group: { _id: null, avgRating: { $avg: '$rating' } } }
    ]);

    // Mood tracking statistics
    const totalMoodEntries = await Mood.countDocuments();
    const moodEntriesThisWeek = await Mood.countDocuments({
      createdAt: { $gte: sevenDaysAgo }
    });

    // Growth metrics
    const userGrowth = await User.aggregate([
      {
        $match: {
          role: 'user',
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    const consultationGrowth = await Consultation.aggregate([
      {
        $match: {
          createdAt: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    const dashboardData = {
      users: {
        total: totalUsers,
        active: activeUsers,
        newThisMonth: newUsersThisMonth,
        growth: userGrowth
      },
      therapists: {
        total: totalTherapists,
        verified: verifiedTherapists,
        active: activeTherapists
      },
      consultations: {
        total: totalConsultations,
        completed: completedConsultations,
        upcoming: upcomingConsultations,
        growth: consultationGrowth
      },
      payments: {
        totalRevenue,
        totalTransactions,
        averageTransactionValue: totalTransactions > 0 ? totalRevenue / totalTransactions : 0
      },
      reviews: {
        total: totalReviews,
        pending: pendingReviews,
        averageRating: averageRating[0]?.avgRating || 0
      },
      moodTracking: {
        totalEntries: totalMoodEntries,
        entriesThisWeek: moodEntriesThisWeek
      }
    };

    res.status(200).json({
      success: true,
      dashboard: dashboardData
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get all users with filtering
// @route   GET /api/admin/users
// @access  Private (Admin only)
router.get('/users', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('role').optional().isIn(['user', 'therapist', 'admin']).withMessage('Invalid role'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  query('search').optional().isString().withMessage('Search must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build query
    const query = {};

    if (req.query.role) {
      query.role = req.query.role;
    }

    if (req.query.isActive !== undefined) {
      query.isActive = req.query.isActive === 'true';
    }

    if (req.query.search) {
      const searchRegex = new RegExp(req.query.search, 'i');
      query.$or = [
        { firstName: searchRegex },
        { lastName: searchRegex },
        { email: searchRegex }
      ];
    }

    const users = await User.find(query)
      .select('-password')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await User.countDocuments(query);

    res.status(200).json({
      success: true,
      count: users.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      users
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update user status
// @route   PUT /api/admin/users/:id/status
// @access  Private (Admin only)
router.put('/users/:id/status', [
  body('isActive').isBoolean().withMessage('isActive must be boolean'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByIdAndUpdate(
      req.params.id,
      { isActive: req.body.isActive },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: `User ${req.body.isActive ? 'activated' : 'deactivated'} successfully`,
      user
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get all therapists with filtering
// @route   GET /api/admin/therapists
// @access  Private (Admin only)
router.get('/therapists', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('isVerified').optional().isBoolean().withMessage('isVerified must be boolean'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    // Build query
    const query = {};

    if (req.query.isVerified !== undefined) {
      query.isVerified = req.query.isVerified === 'true';
    }

    if (req.query.isActive !== undefined) {
      query.isActive = req.query.isActive === 'true';
    }

    const therapists = await Therapist.find(query)
      .populate('user', 'firstName lastName email phone')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Therapist.countDocuments(query);

    res.status(200).json({
      success: true,
      count: therapists.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      therapists
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Verify/Unverify therapist
// @route   PUT /api/admin/therapists/:id/verify
// @access  Private (Admin only)
router.put('/therapists/:id/verify', [
  body('isVerified').isBoolean().withMessage('isVerified must be boolean'),
  body('reason').optional().isString().withMessage('Reason must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const therapist = await Therapist.findByIdAndUpdate(
      req.params.id,
      { isVerified: req.body.isVerified },
      { new: true, runValidators: true }
    ).populate('user', 'firstName lastName email');

    if (!therapist) {
      return res.status(404).json({
        success: false,
        message: 'Therapist not found'
      });
    }

    res.status(200).json({
      success: true,
      message: `Therapist ${req.body.isVerified ? 'verified' : 'unverified'} successfully`,
      therapist
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get all consultations
// @route   GET /api/admin/consultations
// @access  Private (Admin only)
router.get('/consultations', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['scheduled', 'confirmed', 'in-progress', 'completed', 'cancelled', 'no-show']).withMessage('Invalid status')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const query = {};
    if (req.query.status) {
      query.status = req.query.status;
    }

    const consultations = await Consultation.find(query)
      .populate('patient', 'firstName lastName email')
      .populate({
        path: 'therapist',
        populate: { path: 'user', select: 'firstName lastName email' }
      })
      .sort({ scheduledDate: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Consultation.countDocuments(query);

    res.status(200).json({
      success: true,
      count: consultations.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      consultations
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get all reviews for moderation
// @route   GET /api/admin/reviews
// @access  Private (Admin only)
router.get('/reviews', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['pending', 'approved', 'rejected', 'flagged']).withMessage('Invalid status')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const skip = (page - 1) * limit;

    const query = {};
    if (req.query.status) {
      query.status = req.query.status;
    }

    const reviews = await Review.find(query)
      .populate('user', 'firstName lastName email')
      .populate({
        path: 'therapist',
        populate: { path: 'user', select: 'firstName lastName' }
      })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Review.countDocuments(query);

    res.status(200).json({
      success: true,
      count: reviews.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      reviews
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Moderate review (approve/reject)
// @route   PUT /api/admin/reviews/:id/moderate
// @access  Private (Admin only)
router.put('/reviews/:id/moderate', [
  body('action').isIn(['approve', 'reject']).withMessage('Action must be approve or reject'),
  body('reason').optional().isString().withMessage('Reason must be a string'),
  body('notes').optional().isString().withMessage('Notes must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    const { action, reason, notes } = req.body;

    if (action === 'approve') {
      await review.approve(req.user._id, notes);
    } else {
      await review.reject(req.user._id, reason, notes);
    }

    res.status(200).json({
      success: true,
      message: `Review ${action}d successfully`,
      review
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get payment analytics
// @route   GET /api/admin/analytics/payments
// @access  Private (Admin only)
router.get('/analytics/payments', [
  query('startDate').optional().isISO8601().withMessage('Start date must be valid'),
  query('endDate').optional().isISO8601().withMessage('End date must be valid'),
  query('period').optional().isIn(['day', 'week', 'month']).withMessage('Invalid period')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
    const startDate = req.query.startDate ? new Date(req.query.startDate) : new Date(endDate.getTime() - 30 * 24 * 60 * 60 * 1000);

    const paymentStats = await Payment.getStats(startDate, endDate);

    // Get payment trends
    const paymentTrends = await Payment.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: endDate },
          status: 'succeeded'
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          totalAmount: { $sum: '$amount' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    res.status(200).json({
      success: true,
      period: { startDate, endDate },
      stats: paymentStats[0] || {},
      trends: paymentTrends
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
