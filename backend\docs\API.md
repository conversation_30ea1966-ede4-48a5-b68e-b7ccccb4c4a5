# MindEase API Documentation

## Base URL
```
Development: http://localhost:5000/api
Production: https://api.mindease.app/api
```

## Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Response Format
All API responses follow this format:
```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Response data (varies by endpoint)
  "errors": [] // Validation errors (if any)
}
```

## Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user" // optional, defaults to "user"
}
```

#### POST /auth/login
Login with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

#### GET /auth/me
Get current user information (requires authentication).

#### POST /auth/forgot-password
Request password reset email.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

### Mood Tracking

#### POST /moods
Create a new mood entry (requires authentication).

**Request Body:**
```json
{
  "moodLevel": 7,
  "moodType": "happy",
  "emotions": ["joy", "contentment"],
  "triggers": ["work", "exercise"],
  "journalEntry": "Had a great day at work and went for a run.",
  "sleepHours": 8,
  "exerciseMinutes": 30
}
```

#### GET /moods
Get user's mood entries with pagination (requires authentication).

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20)
- `startDate` (optional): Filter from date (ISO 8601)
- `endDate` (optional): Filter to date (ISO 8601)
- `moodType` (optional): Filter by mood type

#### GET /moods/:id
Get specific mood entry (requires authentication).

#### PUT /moods/:id
Update mood entry (requires authentication).

#### DELETE /moods/:id
Delete mood entry (requires authentication).

#### GET /moods/stats/summary
Get mood statistics and insights (requires authentication).

### Therapists

#### GET /therapists
Get list of therapists with filtering.

**Query Parameters:**
- `page` (optional): Page number
- `limit` (optional): Items per page
- `specialization` (optional): Filter by specialization
- `latitude` & `longitude` (optional): Location-based search
- `radius` (optional): Search radius in meters
- `minRating` (optional): Minimum rating filter

#### GET /therapists/:id
Get therapist details.

#### POST /therapists
Create therapist profile (requires therapist authentication).

#### GET /therapists/search/nearby
Search therapists by location.

#### GET /therapists/:id/availability
Get therapist availability.

### Consultations

#### POST /consultations
Book a consultation (requires user authentication).

**Request Body:**
```json
{
  "therapist": "therapist_id",
  "scheduledDate": "2024-01-15T10:00:00Z",
  "duration": 50,
  "type": "video"
}
```

#### GET /consultations
Get user's consultations (requires authentication).

#### GET /consultations/:id
Get consultation details (requires authentication).

#### PUT /consultations/:id/status
Update consultation status (requires authentication).

#### PUT /consultations/:id/reschedule
Reschedule consultation (requires authentication).

### Payments

#### POST /payments/create-intent
Create payment intent for consultation or subscription.

**Request Body:**
```json
{
  "amount": 100.00,
  "currency": "USD",
  "relatedTo": {
    "type": "consultation",
    "id": "consultation_id"
  }
}
```

#### POST /payments/:id/confirm
Confirm payment completion.

#### GET /payments
Get user's payment history.

#### POST /payments/:id/refund
Request payment refund.

### AI Services

#### POST /ai/sentiment
Analyze text sentiment.

**Request Body:**
```json
{
  "text": "I'm feeling really happy today!"
}
```

#### POST /ai/recommendations
Get AI recommendations based on mood data.

#### GET /ai/insights
Get personalized mood insights.

#### POST /ai/crisis-detection
Analyze text for crisis indicators.

### Reviews

#### POST /reviews
Create a review for a therapist.

**Request Body:**
```json
{
  "therapist": "therapist_id",
  "consultation": "consultation_id",
  "rating": 5,
  "title": "Excellent session",
  "comment": "Very helpful and professional.",
  "detailedRatings": {
    "communication": 5,
    "professionalism": 5,
    "helpfulness": 5
  }
}
```

#### GET /reviews/therapist/:therapistId
Get reviews for a specific therapist.

#### GET /reviews/my-reviews
Get user's reviews.

### Admin (Admin only)

#### GET /admin/dashboard
Get admin dashboard statistics.

#### GET /admin/users
Get all users with filtering.

#### PUT /admin/users/:id/status
Update user status (activate/deactivate).

#### GET /admin/therapists
Get all therapists with filtering.

#### PUT /admin/therapists/:id/verify
Verify/unverify therapist.

## Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

## Rate Limiting

API requests are limited to 100 requests per 15-minute window per IP address.

## Webhooks

### Stripe Webhook
**Endpoint:** `/payments/webhook`
**Method:** POST
**Description:** Handles Stripe payment events

## Testing

Use the provided Postman collection for testing all endpoints:
- Import `postman/MindEase_API.postman_collection.json`
- Set up environment variables for base URL and auth token
