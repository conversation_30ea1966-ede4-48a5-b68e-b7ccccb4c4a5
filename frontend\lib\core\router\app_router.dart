import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/register_screen.dart';
import '../../features/auth/presentation/screens/forgot_password_screen.dart';
import '../../features/onboarding/presentation/screens/onboarding_screen.dart';
import '../../features/home/<USER>/screens/home_screen.dart';
import '../../features/mood/presentation/screens/mood_tracking_screen.dart';
import '../../features/mood/presentation/screens/mood_history_screen.dart';
import '../../features/therapist/presentation/screens/therapist_list_screen.dart';
import '../../features/therapist/presentation/screens/therapist_detail_screen.dart';
import '../../features/consultation/presentation/screens/consultation_booking_screen.dart';
import '../../features/consultation/presentation/screens/consultation_list_screen.dart';
import '../../features/consultation/presentation/screens/video_call_screen.dart';
import '../../features/profile/presentation/screens/profile_screen.dart';
import '../../features/profile/presentation/screens/settings_screen.dart';
import '../../main.dart';
import '../services/storage_service.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    redirect: (context, state) async {
      final isLoggedIn = await StorageService.isLoggedIn();
      final isOnboardingCompleted = StorageService.isOnboardingCompleted();

      // If not logged in and trying to access protected routes
      if (!isLoggedIn && !_isPublicRoute(state.uri.toString())) {
        return '/login';
      }

      // If logged in but onboarding not completed
      if (isLoggedIn &&
          !isOnboardingCompleted &&
          state.uri.toString() != '/onboarding') {
        return '/onboarding';
      }

      // If logged in and trying to access auth routes
      if (isLoggedIn && _isAuthRoute(state.uri.toString())) {
        return '/home';
      }

      return null;
    },
    routes: [
      // Splash Screen
      GoRoute(
        path: '/',
        builder: (context, state) => const SplashScreen(),
      ),

      // Onboarding
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),

      // Authentication Routes
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),

      // Main App Routes
      ShellRoute(
        builder: (context, state, child) {
          return MainNavigationWrapper(child: child);
        },
        routes: [
          // Home
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomeScreen(),
          ),

          // Mood Tracking
          GoRoute(
            path: '/mood',
            builder: (context, state) => const MoodTrackingScreen(),
          ),
          GoRoute(
            path: '/mood/history',
            builder: (context, state) => const MoodHistoryScreen(),
          ),

          // Therapists
          GoRoute(
            path: '/therapists',
            builder: (context, state) => const TherapistListScreen(),
          ),
          GoRoute(
            path: '/therapists/:id',
            builder: (context, state) {
              final therapistId = state.pathParameters['id']!;
              return TherapistDetailScreen(therapistId: therapistId);
            },
          ),

          // Consultations
          GoRoute(
            path: '/consultations',
            builder: (context, state) => const ConsultationListScreen(),
          ),
          GoRoute(
            path: '/consultations/book/:therapistId',
            builder: (context, state) {
              final therapistId = state.pathParameters['therapistId']!;
              return ConsultationBookingScreen(therapistId: therapistId);
            },
          ),

          // Profile
          GoRoute(
            path: '/profile',
            builder: (context, state) => const ProfileScreen(),
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),

      // Video Call (Full Screen)
      GoRoute(
        path: '/video-call/:consultationId',
        builder: (context, state) {
          final consultationId = state.pathParameters['consultationId']!;
          return VideoCallScreen(consultationId: consultationId);
        },
      ),
    ],
    errorBuilder: (context, state) => ErrorScreen(error: state.error),
  );
});

// Helper functions
bool _isPublicRoute(String location) {
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/forgot-password',
  ];
  return publicRoutes.contains(location);
}

bool _isAuthRoute(String location) {
  const authRoutes = [
    '/login',
    '/register',
    '/forgot-password',
  ];
  return authRoutes.contains(location);
}

// Main Navigation Wrapper
class MainNavigationWrapper extends StatefulWidget {
  final Widget child;

  const MainNavigationWrapper({
    super.key,
    required this.child,
  });

  @override
  State<MainNavigationWrapper> createState() => _MainNavigationWrapperState();
}

class _MainNavigationWrapperState extends State<MainNavigationWrapper> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
          _navigateToTab(index);
        },
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.mood_outlined),
            activeIcon: Icon(Icons.mood),
            label: 'Mood',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.psychology_outlined),
            activeIcon: Icon(Icons.psychology),
            label: 'Therapists',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.calendar_today_outlined),
            activeIcon: Icon(Icons.calendar_today),
            label: 'Sessions',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }

  void _navigateToTab(int index) {
    switch (index) {
      case 0:
        context.go('/home');
        break;
      case 1:
        context.go('/mood');
        break;
      case 2:
        context.go('/therapists');
        break;
      case 3:
        context.go('/consultations');
        break;
      case 4:
        context.go('/profile');
        break;
    }
  }
}

// Error Screen
class ErrorScreen extends StatelessWidget {
  final Exception? error;

  const ErrorScreen({
    super.key,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Error'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Something went wrong',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error?.toString() ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}
