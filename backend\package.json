{"name": "mindease-backend", "version": "1.0.0", "description": "Backend API for MindEase mental health application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "lint": "eslint src/"}, "keywords": ["mental-health", "ai", "healthcare", "api"], "author": "MindEase Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "multer": "^1.4.5-lts.1", "cloudinary": "^1.40.0", "stripe": "^13.6.0", "nodemailer": "^6.9.4", "socket.io": "^4.7.2", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "node-cron": "^3.0.2", "sentiment": "^5.0.2", "natural": "^6.5.0", "axios": "^1.5.0", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "eslint": "^8.48.0"}, "engines": {"node": ">=16.0.0"}}