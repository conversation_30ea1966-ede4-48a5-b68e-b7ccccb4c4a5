const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const Payment = require('../models/Payment');

class PaymentService {
  async createPaymentIntent(amount, currency, metadata) {
    try {
      const paymentIntent = await stripe.paymentIntents.create({
        amount: Math.round(amount * 100), // Convert to cents
        currency: currency.toLowerCase(),
        metadata,
        automatic_payment_methods: {
          enabled: true
        }
      });

      return paymentIntent;
    } catch (error) {
      throw new Error(`Payment intent creation failed: ${error.message}`);
    }
  }

  async confirmPayment(paymentIntentId) {
    try {
      const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;
    } catch (error) {
      throw new Error(`Payment confirmation failed: ${error.message}`);
    }
  }

  async createRefund(paymentIntentId, amount, reason) {
    try {
      const refund = await stripe.refunds.create({
        payment_intent: paymentIntentId,
        amount: amount ? Math.round(amount * 100) : undefined,
        reason: reason || 'requested_by_customer'
      });

      return refund;
    } catch (error) {
      throw new Error(`Refund creation failed: ${error.message}`);
    }
  }

  async createCustomer(email, name, metadata) {
    try {
      const customer = await stripe.customers.create({
        email,
        name,
        metadata
      });

      return customer;
    } catch (error) {
      throw new Error(`Customer creation failed: ${error.message}`);
    }
  }

  async createSubscription(customerId, priceId, metadata) {
    try {
      const subscription = await stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        metadata,
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent']
      });

      return subscription;
    } catch (error) {
      throw new Error(`Subscription creation failed: ${error.message}`);
    }
  }

  async cancelSubscription(subscriptionId) {
    try {
      const subscription = await stripe.subscriptions.del(subscriptionId);
      return subscription;
    } catch (error) {
      throw new Error(`Subscription cancellation failed: ${error.message}`);
    }
  }

  async handleWebhook(payload, signature) {
    try {
      const event = stripe.webhooks.constructEvent(
        payload,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET
      );

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event.data.object);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object);
          break;
        default:
          console.log(`Unhandled event type: ${event.type}`);
      }

      return { received: true };
    } catch (error) {
      throw new Error(`Webhook handling failed: ${error.message}`);
    }
  }

  async handlePaymentSucceeded(paymentIntent) {
    try {
      const payment = await Payment.findOne({
        'gateway.paymentIntentId': paymentIntent.id
      });

      if (payment) {
        await payment.markAsSucceeded({
          chargeId: paymentIntent.latest_charge,
          receiptUrl: paymentIntent.charges?.data[0]?.receipt_url
        });
      }
    } catch (error) {
      console.error('Error handling payment succeeded:', error);
    }
  }

  async handlePaymentFailed(paymentIntent) {
    try {
      const payment = await Payment.findOne({
        'gateway.paymentIntentId': paymentIntent.id
      });

      if (payment) {
        await payment.markAsFailed({
          code: paymentIntent.last_payment_error?.code,
          message: paymentIntent.last_payment_error?.message,
          declineCode: paymentIntent.last_payment_error?.decline_code
        });
      }
    } catch (error) {
      console.error('Error handling payment failed:', error);
    }
  }

  async handleInvoicePaymentSucceeded(invoice) {
    // Handle subscription payment success
    console.log('Invoice payment succeeded:', invoice.id);
  }

  async handleSubscriptionDeleted(subscription) {
    // Handle subscription cancellation
    console.log('Subscription deleted:', subscription.id);
  }

  // Utility methods
  formatAmount(amount, currency) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount);
  }

  validateAmount(amount) {
    if (typeof amount !== 'number' || amount <= 0) {
      throw new Error('Invalid amount');
    }
    if (amount < 0.50) {
      throw new Error('Amount must be at least $0.50');
    }
    return true;
  }

  validateCurrency(currency) {
    const supportedCurrencies = ['usd', 'eur', 'gbp'];
    if (!supportedCurrencies.includes(currency.toLowerCase())) {
      throw new Error('Unsupported currency');
    }
    return true;
  }
}

module.exports = new PaymentService();
