const express = require('express');
const { body, validationResult, query } = require('express-validator');
const Review = require('../models/Review');
const Consultation = require('../models/Consultation');
const Therapist = require('../models/Therapist');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// @desc    Create a review
// @route   POST /api/reviews
// @access  Private (User only)
router.post('/', protect, authorize('user'), [
  body('therapist').isMongoId().withMessage('Valid therapist ID is required'),
  body('consultation').isMongoId().withMessage('Valid consultation ID is required'),
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('title').trim().isLength({ min: 5, max: 100 }).withMessage('Title must be between 5 and 100 characters'),
  body('comment').trim().isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters'),
  body('detailedRatings.communication').optional().isInt({ min: 1, max: 5 }).withMessage('Communication rating must be between 1 and 5'),
  body('detailedRatings.professionalism').optional().isInt({ min: 1, max: 5 }).withMessage('Professionalism rating must be between 1 and 5'),
  body('detailedRatings.helpfulness').optional().isInt({ min: 1, max: 5 }).withMessage('Helpfulness rating must be between 1 and 5'),
  body('detailedRatings.punctuality').optional().isInt({ min: 1, max: 5 }).withMessage('Punctuality rating must be between 1 and 5'),
  body('isAnonymous').optional().isBoolean().withMessage('Anonymous flag must be boolean')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { therapist: therapistId, consultation: consultationId } = req.body;

    // Check if consultation exists and belongs to user
    const consultation = await Consultation.findById(consultationId);
    if (!consultation) {
      return res.status(404).json({
        success: false,
        message: 'Consultation not found'
      });
    }

    if (consultation.patient.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only review your own consultations'
      });
    }

    if (consultation.status !== 'completed') {
      return res.status(400).json({
        success: false,
        message: 'You can only review completed consultations'
      });
    }

    // Check if therapist exists
    const therapist = await Therapist.findById(therapistId);
    if (!therapist) {
      return res.status(404).json({
        success: false,
        message: 'Therapist not found'
      });
    }

    // Check if review already exists for this consultation
    const existingReview = await Review.findOne({ consultation: consultationId });
    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'Review already exists for this consultation'
      });
    }

    // Create review
    const reviewData = {
      ...req.body,
      user: req.user._id,
      source: 'mobile_app'
    };

    const review = await Review.create(reviewData);
    await review.populate([
      { path: 'user', select: 'firstName lastName avatar' },
      { path: 'therapist', populate: { path: 'user', select: 'firstName lastName' } }
    ]);

    res.status(201).json({
      success: true,
      message: 'Review created successfully',
      review
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get reviews for a therapist
// @route   GET /api/reviews/therapist/:therapistId
// @access  Public
router.get('/therapist/:therapistId', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('rating').optional().isInt({ min: 1, max: 5 }).withMessage('Rating filter must be between 1 and 5'),
  query('sortBy').optional().isIn(['newest', 'oldest', 'highest', 'lowest', 'helpful']).withMessage('Invalid sort option')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { therapistId } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build query
    const query = {
      therapist: therapistId,
      status: 'approved'
    };

    if (req.query.rating) {
      query.rating = parseInt(req.query.rating);
    }

    // Build sort options
    let sortOptions = { createdAt: -1 }; // Default: newest first
    
    switch (req.query.sortBy) {
      case 'oldest':
        sortOptions = { createdAt: 1 };
        break;
      case 'highest':
        sortOptions = { rating: -1, createdAt: -1 };
        break;
      case 'lowest':
        sortOptions = { rating: 1, createdAt: -1 };
        break;
      case 'helpful':
        // This would require a more complex aggregation for helpfulness score
        sortOptions = { createdAt: -1 };
        break;
    }

    const reviews = await Review.find(query)
      .populate('user', 'firstName lastName avatar')
      .sort(sortOptions)
      .skip(skip)
      .limit(limit);

    const total = await Review.countDocuments(query);

    // Get review statistics
    const stats = await Review.getTherapistStats(therapistId);

    res.status(200).json({
      success: true,
      count: reviews.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      reviews,
      stats: stats[0] || { averageRating: 0, totalReviews: 0 }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user's reviews
// @route   GET /api/reviews/my-reviews
// @access  Private
router.get('/my-reviews', protect, async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const reviews = await Review.find({ user: req.user._id })
      .populate('therapist', 'user specializations')
      .populate('consultation', 'scheduledDate type')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await Review.countDocuments({ user: req.user._id });

    res.status(200).json({
      success: true,
      count: reviews.length,
      total,
      page,
      pages: Math.ceil(total / limit),
      reviews
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update a review
// @route   PUT /api/reviews/:id
// @access  Private (Own review only)
router.put('/:id', protect, [
  body('rating').optional().isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('title').optional().trim().isLength({ min: 5, max: 100 }).withMessage('Title must be between 5 and 100 characters'),
  body('comment').optional().trim().isLength({ min: 10, max: 1000 }).withMessage('Comment must be between 10 and 1000 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    let review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if user owns this review
    if (review.user.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only update your own reviews'
      });
    }

    // Don't allow updating if review is rejected
    if (review.status === 'rejected') {
      return res.status(400).json({
        success: false,
        message: 'Cannot update rejected reviews'
      });
    }

    // Update review and reset status to pending if it was approved
    const updateData = req.body;
    if (review.status === 'approved') {
      updateData.status = 'pending';
    }

    review = await Review.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).populate([
      { path: 'user', select: 'firstName lastName avatar' },
      { path: 'therapist', populate: { path: 'user', select: 'firstName lastName' } }
    ]);

    res.status(200).json({
      success: true,
      message: 'Review updated successfully',
      review
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete a review
// @route   DELETE /api/reviews/:id
// @access  Private (Own review only)
router.delete('/:id', protect, async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if user owns this review or is admin
    if (review.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    await review.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Review deleted successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Mark review as helpful
// @route   POST /api/reviews/:id/helpful
// @access  Private
router.post('/:id/helpful', protect, async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    if (review.status !== 'approved') {
      return res.status(400).json({
        success: false,
        message: 'Can only rate approved reviews'
      });
    }

    await review.markAsHelpful(req.user._id);

    res.status(200).json({
      success: true,
      message: 'Review marked as helpful',
      helpfulnessScore: review.helpfulnessScore
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Mark review as not helpful
// @route   POST /api/reviews/:id/not-helpful
// @access  Private
router.post('/:id/not-helpful', protect, async (req, res, next) => {
  try {
    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    if (review.status !== 'approved') {
      return res.status(400).json({
        success: false,
        message: 'Can only rate approved reviews'
      });
    }

    await review.markAsNotHelpful(req.user._id);

    res.status(200).json({
      success: true,
      message: 'Review marked as not helpful',
      helpfulnessScore: review.helpfulnessScore
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Flag a review
// @route   POST /api/reviews/:id/flag
// @access  Private
router.post('/:id/flag', protect, [
  body('reason').isIn(['inappropriate', 'spam', 'fake', 'offensive', 'other']).withMessage('Invalid flag reason'),
  body('description').optional().isLength({ max: 500 }).withMessage('Description cannot exceed 500 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    const { reason, description } = req.body;

    try {
      await review.flagReview(req.user._id, reason, description);

      res.status(200).json({
        success: true,
        message: 'Review flagged successfully'
      });
    } catch (error) {
      if (error.message === 'You have already flagged this review') {
        return res.status(400).json({
          success: false,
          message: error.message
        });
      }
      throw error;
    }
  } catch (error) {
    next(error);
  }
});

// @desc    Add therapist response to review
// @route   POST /api/reviews/:id/response
// @access  Private (Therapist only)
router.post('/:id/response', protect, authorize('therapist'), [
  body('comment').trim().isLength({ min: 10, max: 500 }).withMessage('Response must be between 10 and 500 characters')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const review = await Review.findById(req.params.id);

    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    // Check if therapist owns this review
    const therapist = await Therapist.findOne({ user: req.user._id });
    if (!therapist || review.therapist.toString() !== therapist._id.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only respond to your own reviews'
      });
    }

    if (review.therapistResponse.comment) {
      return res.status(400).json({
        success: false,
        message: 'Response already exists for this review'
      });
    }

    review.therapistResponse = {
      comment: req.body.comment,
      respondedAt: new Date()
    };

    await review.save();

    res.status(200).json({
      success: true,
      message: 'Response added successfully',
      response: review.therapistResponse
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
