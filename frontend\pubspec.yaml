name: mindease
description: AI-powered mental health application

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  lottie: ^2.7.0
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.1.1

  # State Management
  provider: ^6.1.1
  flutter_riverpod: ^2.4.9

  # Navigation
  go_router: ^12.1.3

  # HTTP and API
  http: ^1.1.2
  dio: ^5.4.0
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  sqflite: ^2.3.0

  # Authentication
  local_auth: ^2.1.7
  crypto: ^3.0.3

  # Location and Maps
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.3
  location: ^5.0.3

  # Media and Files
  image_picker: ^1.0.4
  file_picker: ^6.1.1
  cached_network_image: ^3.3.0
  photo_view: ^0.14.0

  # Charts and Analytics
  fl_chart: ^0.65.0
  syncfusion_flutter_charts: ^24.2.9

  # Date and Time
  intl: ^0.19.0
  table_calendar: ^3.0.9

  # Notifications
  flutter_local_notifications: ^16.3.2
  # firebase_messaging: ^14.7.10

  # Video Calling
  # agora_rtc_engine: ^6.3.2
  # permission_handler: ^11.1.0

  # Payment
  # stripe_payment: ^1.1.4
  # pay: ^1.1.2

  # Utils
  uuid: ^4.2.1
  url_launcher: ^6.2.2
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.1
  connectivity_plus: ^5.0.2

  # Firebase
  # firebase_core: ^2.24.2
  # firebase_auth: ^4.15.3
  # firebase_analytics: ^10.7.4
  # cloud_firestore: ^4.13.6

  # Animations
  rive: ^0.12.4
  flutter_animate: ^4.3.0

  # Form Validation
  form_validator: ^2.1.1

  # Biometric Authentication
  flutter_secure_storage: ^9.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf
  #       - asset: assets/fonts/Poppins-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Poppins-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Poppins-Bold.ttf
  #         weight: 700
