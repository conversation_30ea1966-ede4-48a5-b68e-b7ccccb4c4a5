# MindEase - AI-Powered Mental Health Application

## Overview
MindEase is a comprehensive mental health application that provides personalized support through advanced AI features, mood tracking, therapist connections, and secure consultations.

## Features
- 🧠 AI-powered mood tracking and analysis
- 📝 Daily journaling with sentiment analysis
- 🔍 Therapist finder with geolocation
- 💬 Online video consultations
- 💳 Secure in-app payments
- 📊 Personalized recommendations
- 👥 User reviews and ratings
- 🔒 GDPR and HIPAA compliant

## Technology Stack
- **Frontend**: Flutter (Mobile)
- **Backend**: Node.js with Express
- **Database**: MongoDB
- **AI/ML**: Sentiment analysis and recommendation engine
- **Payment**: Stripe integration
- **Maps**: Google Maps API

## Project Structure
```
mindease/
├── backend/                 # Node.js/Express backend
│   ├── src/
│   ├── package.json
│   └── ...
├── frontend/               # Flutter mobile app
│   ├── lib/
│   ├── pubspec.yaml
│   └── ...
├── admin-panel/           # Web-based admin panel
└── docs/                  # Documentation
```

## Getting Started

### Backend Setup
```bash
cd backend
npm install
npm run dev
```

### Frontend Setup
```bash
cd frontend
flutter pub get
flutter run
```

## API Documentation
The backend provides RESTful APIs for all features. See `/backend/docs/api.md` for detailed documentation.

## Contributing
Please read our contributing guidelines before submitting pull requests.

## License
This project is licensed under the MIT License.
