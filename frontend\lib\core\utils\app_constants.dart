class AppConstants {
  // App Information
  static const String appName = 'MindEase';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Your AI-Powered Mental Health Companion';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:5000/api';
  static const String socketUrl = 'http://localhost:5000';
  
  // Storage Keys
  static const String authTokenKey = 'auth_token';
  static const String userDataKey = 'user_data';
  static const String onboardingCompletedKey = 'onboarding_completed';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  
  // Hive Box Names
  static const String userBox = 'user_box';
  static const String moodBox = 'mood_box';
  static const String settingsBox = 'settings_box';
  static const String cacheBox = 'cache_box';
  
  // Animation Durations
  static const Duration shortAnimationDuration = Duration(milliseconds: 200);
  static const Duration mediumAnimationDuration = Duration(milliseconds: 400);
  static const Duration longAnimationDuration = Duration(milliseconds: 600);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double extraLargePadding = 32.0;
  
  static const double defaultBorderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 16.0;
  static const double extraLargeBorderRadius = 24.0;
  
  // Mood Levels
  static const Map<int, String> moodLevels = {
    1: 'Very Sad',
    2: 'Sad',
    3: 'Okay',
    4: 'Good',
    5: 'Great',
    6: 'Happy',
    7: 'Very Happy',
    8: 'Excited',
    9: 'Euphoric',
    10: 'Amazing',
  };
  
  static const Map<String, String> moodTypes = {
    'very-sad': 'Very Sad',
    'sad': 'Sad',
    'neutral': 'Neutral',
    'happy': 'Happy',
    'very-happy': 'Very Happy',
    'anxious': 'Anxious',
    'stressed': 'Stressed',
    'calm': 'Calm',
    'excited': 'Excited',
    'angry': 'Angry',
    'frustrated': 'Frustrated',
    'content': 'Content',
    'overwhelmed': 'Overwhelmed',
    'peaceful': 'Peaceful',
    'energetic': 'Energetic',
  };
  
  // Emotions
  static const List<String> emotions = [
    'joy',
    'sadness',
    'anger',
    'fear',
    'surprise',
    'disgust',
    'anxiety',
    'stress',
    'excitement',
    'contentment',
    'frustration',
    'hope',
    'loneliness',
    'gratitude',
    'guilt',
    'shame',
    'pride',
    'love',
    'jealousy',
    'envy',
    'compassion',
    'empathy',
  ];
  
  // Triggers
  static const List<String> triggers = [
    'work',
    'relationships',
    'family',
    'health',
    'finances',
    'social-media',
    'news',
    'weather',
    'sleep',
    'exercise',
    'food',
    'medication',
    'therapy',
    'social-interaction',
    'isolation',
    'achievement',
    'failure',
    'change',
    'routine',
  ];
  
  // Activities
  static const List<String> activities = [
    'meditation',
    'exercise',
    'reading',
    'music',
    'art',
    'socializing',
    'working',
    'studying',
    'cooking',
    'cleaning',
    'gaming',
    'watching-tv',
    'walking',
    'sleeping',
    'eating',
    'therapy',
    'journaling',
    'breathing-exercises',
    'yoga',
  ];
  
  // Therapist Specializations
  static const List<String> therapistSpecializations = [
    'anxiety-disorders',
    'depression',
    'trauma-ptsd',
    'addiction',
    'eating-disorders',
    'bipolar-disorder',
    'ocd',
    'adhd',
    'relationship-counseling',
    'family-therapy',
    'child-therapy',
    'adolescent-therapy',
    'grief-counseling',
    'stress-management',
    'anger-management',
    'sleep-disorders',
    'personality-disorders',
    'autism-spectrum',
    'lgbtq-issues',
    'cultural-issues',
  ];
  
  // Therapy Approaches
  static const List<String> therapyApproaches = [
    'cognitive-behavioral-therapy',
    'dialectical-behavior-therapy',
    'psychodynamic-therapy',
    'humanistic-therapy',
    'gestalt-therapy',
    'solution-focused-therapy',
    'mindfulness-based-therapy',
    'acceptance-commitment-therapy',
    'emdr',
    'family-systems-therapy',
  ];
  
  // Consultation Types
  static const List<String> consultationTypes = [
    'video',
    'audio',
    'in-person',
    'chat',
  ];
  
  // Payment Methods
  static const List<String> paymentMethods = [
    'credit-card',
    'debit-card',
    'paypal',
    'apple-pay',
    'google-pay',
  ];
  
  // Subscription Plans
  static const Map<String, Map<String, dynamic>> subscriptionPlans = {
    'free': {
      'name': 'Free',
      'price': 0,
      'features': [
        'Basic mood tracking',
        'Limited AI insights',
        'Community support',
      ],
    },
    'premium': {
      'name': 'Premium',
      'price': 9.99,
      'features': [
        'Advanced mood tracking',
        'Unlimited AI insights',
        'Therapist finder',
        'Basic consultations',
        'Priority support',
      ],
    },
    'pro': {
      'name': 'Pro',
      'price': 19.99,
      'features': [
        'Everything in Premium',
        'Unlimited consultations',
        'Advanced analytics',
        'Personal AI coach',
        'Family sharing',
        '24/7 crisis support',
      ],
    },
  };
  
  // Error Messages
  static const String networkErrorMessage = 'Please check your internet connection and try again.';
  static const String serverErrorMessage = 'Something went wrong. Please try again later.';
  static const String authErrorMessage = 'Authentication failed. Please login again.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  
  // Success Messages
  static const String loginSuccessMessage = 'Welcome back!';
  static const String registrationSuccessMessage = 'Account created successfully!';
  static const String moodSavedMessage = 'Mood entry saved successfully!';
  static const String profileUpdatedMessage = 'Profile updated successfully!';
  
  // Validation Rules
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxJournalLength = 2000;
  static const int maxReviewLength = 1000;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // File Upload
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  
  // Emergency Contacts
  static const Map<String, String> emergencyContacts = {
    'suicide_prevention': '988',
    'crisis_text_line': 'Text HOME to 741741',
    'emergency': '911',
  };
  
  // App URLs
  static const String privacyPolicyUrl = 'https://mindease.app/privacy';
  static const String termsOfServiceUrl = 'https://mindease.app/terms';
  static const String supportUrl = 'https://mindease.app/support';
  static const String websiteUrl = 'https://mindease.app';
}
