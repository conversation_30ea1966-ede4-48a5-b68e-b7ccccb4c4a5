import 'dart:convert';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../utils/app_constants.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static late Box _userBox;
  static late Box _moodBox;
  static late Box _settingsBox;
  static late Box _cacheBox;
  
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static Future<void> init() async {
    // Initialize SharedPreferences
    _prefs = await SharedPreferences.getInstance();
    
    // Initialize Hive boxes
    _userBox = await Hive.openBox(AppConstants.userBox);
    _moodBox = await Hive.openBox(AppConstants.moodBox);
    _settingsBox = await Hive.openBox(AppConstants.settingsBox);
    _cacheBox = await Hive.openBox(AppConstants.cacheBox);
  }

  // Secure Storage Methods (for sensitive data like tokens)
  static Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  static Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }

  static Future<void> deleteSecureString(String key) async {
    await _secureStorage.delete(key: key);
  }

  static Future<void> clearSecureStorage() async {
    await _secureStorage.deleteAll();
  }

  // SharedPreferences Methods (for simple key-value pairs)
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs.getString(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  static Future<void> setStringList(String key, List<String> value) async {
    await _prefs.setStringList(key, value);
  }

  static List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  static Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  static Future<void> clear() async {
    await _prefs.clear();
  }

  // Hive Box Methods (for complex objects)
  
  // User Box Methods
  static Future<void> setUserData(String key, dynamic value) async {
    await _userBox.put(key, value);
  }

  static dynamic getUserData(String key) {
    return _userBox.get(key);
  }

  static Future<void> deleteUserData(String key) async {
    await _userBox.delete(key);
  }

  static Future<void> clearUserData() async {
    await _userBox.clear();
  }

  // Mood Box Methods
  static Future<void> setMoodData(String key, dynamic value) async {
    await _moodBox.put(key, value);
  }

  static dynamic getMoodData(String key) {
    return _moodBox.get(key);
  }

  static List<dynamic> getAllMoodData() {
    return _moodBox.values.toList();
  }

  static Future<void> deleteMoodData(String key) async {
    await _moodBox.delete(key);
  }

  static Future<void> clearMoodData() async {
    await _moodBox.clear();
  }

  // Settings Box Methods
  static Future<void> setSetting(String key, dynamic value) async {
    await _settingsBox.put(key, value);
  }

  static dynamic getSetting(String key) {
    return _settingsBox.get(key);
  }

  static Future<void> deleteSetting(String key) async {
    await _settingsBox.delete(key);
  }

  static Future<void> clearSettings() async {
    await _settingsBox.clear();
  }

  // Cache Box Methods
  static Future<void> setCacheData(String key, dynamic value, {Duration? expiry}) async {
    final cacheItem = {
      'data': value,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await _cacheBox.put(key, cacheItem);
  }

  static dynamic getCacheData(String key) {
    final cacheItem = _cacheBox.get(key);
    if (cacheItem == null) return null;

    final timestamp = cacheItem['timestamp'] as int;
    final expiry = cacheItem['expiry'] as int?;

    if (expiry != null) {
      final expiryTime = timestamp + expiry;
      if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
        _cacheBox.delete(key);
        return null;
      }
    }

    return cacheItem['data'];
  }

  static Future<void> deleteCacheData(String key) async {
    await _cacheBox.delete(key);
  }

  static Future<void> clearCache() async {
    await _cacheBox.clear();
  }

  static Future<void> clearExpiredCache() async {
    final keys = _cacheBox.keys.toList();
    for (final key in keys) {
      getCacheData(key); // This will automatically delete expired items
    }
  }

  // JSON Methods
  static Future<void> setJson(String key, Map<String, dynamic> value) async {
    await setString(key, jsonEncode(value));
  }

  static Map<String, dynamic>? getJson(String key) {
    final jsonString = getString(key);
    if (jsonString == null) return null;
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // Authentication Methods
  static Future<void> setAuthToken(String token) async {
    await setSecureString(AppConstants.authTokenKey, token);
  }

  static Future<String?> getAuthToken() async {
    return await getSecureString(AppConstants.authTokenKey);
  }

  static Future<void> clearAuthToken() async {
    await deleteSecureString(AppConstants.authTokenKey);
  }

  static Future<bool> isLoggedIn() async {
    final token = await getAuthToken();
    return token != null && token.isNotEmpty;
  }

  // User Preferences Methods
  static Future<void> setOnboardingCompleted(bool completed) async {
    await setBool(AppConstants.onboardingCompletedKey, completed);
  }

  static bool isOnboardingCompleted() {
    return getBool(AppConstants.onboardingCompletedKey) ?? false;
  }

  static Future<void> setBiometricEnabled(bool enabled) async {
    await setBool(AppConstants.biometricEnabledKey, enabled);
  }

  static bool isBiometricEnabled() {
    return getBool(AppConstants.biometricEnabledKey) ?? false;
  }

  static Future<void> setNotificationsEnabled(bool enabled) async {
    await setBool(AppConstants.notificationsEnabledKey, enabled);
  }

  static bool areNotificationsEnabled() {
    return getBool(AppConstants.notificationsEnabledKey) ?? true;
  }

  static Future<void> setThemeMode(String theme) async {
    await setString(AppConstants.themeKey, theme);
  }

  static String getThemeMode() {
    return getString(AppConstants.themeKey) ?? 'system';
  }

  static Future<void> setLanguage(String language) async {
    await setString(AppConstants.languageKey, language);
  }

  static String getLanguage() {
    return getString(AppConstants.languageKey) ?? 'en';
  }

  // Cleanup Methods
  static Future<void> clearAllData() async {
    await clearSecureStorage();
    await clear();
    await clearUserData();
    await clearMoodData();
    await clearSettings();
    await clearCache();
  }

  static Future<void> clearUserSession() async {
    await clearAuthToken();
    await clearUserData();
  }

  // Backup and Restore Methods
  static Map<String, dynamic> exportUserData() {
    return {
      'userData': _userBox.toMap(),
      'moodData': _moodBox.toMap(),
      'settings': _settingsBox.toMap(),
      'preferences': {
        'onboardingCompleted': isOnboardingCompleted(),
        'biometricEnabled': isBiometricEnabled(),
        'notificationsEnabled': areNotificationsEnabled(),
        'themeMode': getThemeMode(),
        'language': getLanguage(),
      },
    };
  }

  static Future<void> importUserData(Map<String, dynamic> data) async {
    // Import user data
    if (data['userData'] != null) {
      final userData = data['userData'] as Map;
      for (final entry in userData.entries) {
        await setUserData(entry.key, entry.value);
      }
    }

    // Import mood data
    if (data['moodData'] != null) {
      final moodData = data['moodData'] as Map;
      for (final entry in moodData.entries) {
        await setMoodData(entry.key, entry.value);
      }
    }

    // Import settings
    if (data['settings'] != null) {
      final settings = data['settings'] as Map;
      for (final entry in settings.entries) {
        await setSetting(entry.key, entry.value);
      }
    }

    // Import preferences
    if (data['preferences'] != null) {
      final preferences = data['preferences'] as Map<String, dynamic>;
      if (preferences['onboardingCompleted'] != null) {
        await setOnboardingCompleted(preferences['onboardingCompleted']);
      }
      if (preferences['biometricEnabled'] != null) {
        await setBiometricEnabled(preferences['biometricEnabled']);
      }
      if (preferences['notificationsEnabled'] != null) {
        await setNotificationsEnabled(preferences['notificationsEnabled']);
      }
      if (preferences['themeMode'] != null) {
        await setThemeMode(preferences['themeMode']);
      }
      if (preferences['language'] != null) {
        await setLanguage(preferences['language']);
      }
    }
  }
}
