const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const Mood = require('../models/Mood');
const { protect, ownerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);

// @desc    Get user profile
// @route   GET /api/users/:id
// @access  Private (Own profile or Admin)
router.get('/:id', ownerOrAdmin(), async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      user
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update user profile
// @route   PUT /api/users/:id
// @access  Private (Own profile or Admin)
router.put('/:id', ownerOrAdmin(), [
  body('firstName').optional().trim().isLength({ min: 2 }).withMessage('First name must be at least 2 characters'),
  body('lastName').optional().trim().isLength({ min: 2 }).withMessage('Last name must be at least 2 characters'),
  body('phone').optional().matches(/^\+?[\d\s-()]+$/).withMessage('Please enter a valid phone number'),
  body('dateOfBirth').optional().isISO8601().withMessage('Please enter a valid date of birth'),
  body('gender').optional().isIn(['male', 'female', 'other', 'prefer-not-to-say']).withMessage('Invalid gender option')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    // Don't allow updating sensitive fields
    const allowedFields = [
      'firstName', 'lastName', 'phone', 'dateOfBirth', 'gender',
      'emergencyContact', 'mentalHealthHistory', 'preferences', 'location'
    ];

    const updateData = {};
    Object.keys(req.body).forEach(key => {
      if (allowedFields.includes(key)) {
        updateData[key] = req.body[key];
      }
    });

    const user = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Profile updated successfully',
      user
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Delete user account
// @route   DELETE /api/users/:id
// @access  Private (Own profile or Admin)
router.delete('/:id', ownerOrAdmin(), async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Soft delete - deactivate account instead of removing
    user.isActive = false;
    await user.save();

    res.status(200).json({
      success: true,
      message: 'Account deactivated successfully'
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get user dashboard data
// @route   GET /api/users/:id/dashboard
// @access  Private (Own profile or Admin)
router.get('/:id/dashboard', ownerOrAdmin(), async (req, res, next) => {
  try {
    const userId = req.params.id;

    // Get recent mood entries
    const recentMoods = await Mood.find({ user: userId })
      .sort({ date: -1 })
      .limit(7)
      .select('moodLevel moodType date emotions');

    // Calculate mood statistics
    const moodStats = await Mood.aggregate([
      { $match: { user: mongoose.Types.ObjectId(userId) } },
      {
        $group: {
          _id: null,
          averageMood: { $avg: '$moodLevel' },
          totalEntries: { $sum: 1 },
          moodDistribution: {
            $push: {
              mood: '$moodType',
              level: '$moodLevel'
            }
          }
        }
      }
    ]);

    // Get mood trends for the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const moodTrends = await Mood.aggregate([
      {
        $match: {
          user: mongoose.Types.ObjectId(userId),
          date: { $gte: thirtyDaysAgo }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$date' }
          },
          averageMood: { $avg: '$moodLevel' },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    const dashboardData = {
      recentMoods,
      stats: moodStats[0] || { averageMood: 0, totalEntries: 0, moodDistribution: [] },
      trends: moodTrends,
      streaks: {
        current: calculateCurrentStreak(recentMoods),
        longest: await calculateLongestStreak(userId)
      }
    };

    res.status(200).json({
      success: true,
      dashboard: dashboardData
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update user preferences
// @route   PUT /api/users/:id/preferences
// @access  Private (Own profile or Admin)
router.put('/:id/preferences', ownerOrAdmin(), [
  body('notifications').optional().isObject().withMessage('Notifications must be an object'),
  body('privacy').optional().isObject().withMessage('Privacy must be an object'),
  body('therapistPreferences').optional().isObject().withMessage('Therapist preferences must be an object')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByIdAndUpdate(
      req.params.id,
      { preferences: req.body },
      { new: true, runValidators: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Preferences updated successfully',
      preferences: user.preferences
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Update user location
// @route   PUT /api/users/:id/location
// @access  Private (Own profile or Admin)
router.put('/:id/location', ownerOrAdmin(), [
  body('latitude').isFloat({ min: -90, max: 90 }).withMessage('Valid latitude is required'),
  body('longitude').isFloat({ min: -180, max: 180 }).withMessage('Valid longitude is required'),
  body('address').optional().isString().withMessage('Address must be a string'),
  body('city').optional().isString().withMessage('City must be a string'),
  body('state').optional().isString().withMessage('State must be a string'),
  body('country').optional().isString().withMessage('Country must be a string'),
  body('zipCode').optional().isString().withMessage('Zip code must be a string')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { latitude, longitude, address, city, state, country, zipCode } = req.body;

    const locationData = {
      location: {
        type: 'Point',
        coordinates: [longitude, latitude],
        address,
        city,
        state,
        country,
        zipCode
      }
    };

    const user = await User.findByIdAndUpdate(
      req.params.id,
      locationData,
      { new: true, runValidators: true }
    );

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.status(200).json({
      success: true,
      message: 'Location updated successfully',
      location: user.location
    });
  } catch (error) {
    next(error);
  }
});

// Helper function to calculate current mood tracking streak
function calculateCurrentStreak(moods) {
  if (!moods || moods.length === 0) return 0;

  let streak = 0;
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  for (let i = 0; i < moods.length; i++) {
    const moodDate = new Date(moods[i].date);
    moodDate.setHours(0, 0, 0, 0);
    
    const expectedDate = new Date(today);
    expectedDate.setDate(today.getDate() - i);

    if (moodDate.getTime() === expectedDate.getTime()) {
      streak++;
    } else {
      break;
    }
  }

  return streak;
}

// Helper function to calculate longest mood tracking streak
async function calculateLongestStreak(userId) {
  try {
    const allMoods = await Mood.find({ user: userId })
      .sort({ date: 1 })
      .select('date');

    if (allMoods.length === 0) return 0;

    let longestStreak = 1;
    let currentStreak = 1;

    for (let i = 1; i < allMoods.length; i++) {
      const currentDate = new Date(allMoods[i].date);
      const previousDate = new Date(allMoods[i - 1].date);
      
      currentDate.setHours(0, 0, 0, 0);
      previousDate.setHours(0, 0, 0, 0);
      
      const dayDifference = (currentDate - previousDate) / (1000 * 60 * 60 * 24);

      if (dayDifference === 1) {
        currentStreak++;
        longestStreak = Math.max(longestStreak, currentStreak);
      } else {
        currentStreak = 1;
      }
    }

    return longestStreak;
  } catch (error) {
    console.error('Error calculating longest streak:', error);
    return 0;
  }
}

module.exports = router;
