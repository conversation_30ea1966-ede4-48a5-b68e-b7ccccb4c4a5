const express = require('express');
const { body, validationResult } = require('express-validator');
const Mood = require('../models/Mood');
const { protect, requireVerification } = require('../middleware/auth');
const { 
  analyzeSentiment, 
  generateRecommendations, 
  generateMoodInsights 
} = require('../services/aiService');

const router = express.Router();

// Apply authentication to all routes
router.use(protect);
router.use(requireVerification);

// @desc    Analyze sentiment of text
// @route   POST /api/ai/sentiment
// @access  Private
router.post('/sentiment', [
  body('text').notEmpty().withMessage('Text is required for sentiment analysis')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { text } = req.body;
    
    const sentimentResult = await analyzeSentiment(text);

    res.status(200).json({
      success: true,
      sentiment: sentimentResult
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get AI recommendations based on mood data
// @route   POST /api/ai/recommendations
// @access  Private
router.post('/recommendations', [
  body('moodLevel').isInt({ min: 1, max: 10 }).withMessage('Mood level must be between 1 and 10'),
  body('moodType').notEmpty().withMessage('Mood type is required'),
  body('emotions').optional().isArray().withMessage('Emotions must be an array'),
  body('triggers').optional().isArray().withMessage('Triggers must be an array')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const moodData = req.body;
    
    // Get user's mood history for better recommendations
    const userMoodHistory = await Mood.find({ user: req.user._id })
      .sort({ date: -1 })
      .limit(30)
      .select('moodLevel moodType emotions triggers date');

    const recommendations = await generateRecommendations(moodData, userMoodHistory);

    res.status(200).json({
      success: true,
      recommendations
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get mood insights and patterns
// @route   GET /api/ai/insights
// @access  Private
router.get('/insights', async (req, res, next) => {
  try {
    // Get user's mood history
    const moodHistory = await Mood.find({ user: req.user._id })
      .sort({ date: -1 })
      .limit(90) // Last 90 entries
      .select('moodLevel moodType emotions triggers date journalEntry sentimentAnalysis');

    const insights = await generateMoodInsights(moodHistory);

    res.status(200).json({
      success: true,
      insights
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get personalized recommendations based on user history
// @route   GET /api/ai/personalized-recommendations
// @access  Private
router.get('/personalized-recommendations', async (req, res, next) => {
  try {
    // Get recent mood entries
    const recentMoods = await Mood.find({ user: req.user._id })
      .sort({ date: -1 })
      .limit(7)
      .select('moodLevel moodType emotions triggers sentimentAnalysis');

    if (recentMoods.length === 0) {
      return res.status(200).json({
        success: true,
        message: 'Start tracking your mood to get personalized recommendations',
        recommendations: []
      });
    }

    // Calculate average mood and common patterns
    const averageMood = recentMoods.reduce((sum, mood) => sum + mood.moodLevel, 0) / recentMoods.length;
    const allEmotions = recentMoods.flatMap(mood => mood.emotions || []);
    const allTriggers = recentMoods.flatMap(mood => mood.triggers || []);
    
    // Get most common emotions and triggers
    const emotionCounts = {};
    const triggerCounts = {};
    
    allEmotions.forEach(emotion => {
      emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
    });
    
    allTriggers.forEach(trigger => {
      triggerCounts[trigger] = (triggerCounts[trigger] || 0) + 1;
    });
    
    const commonEmotions = Object.entries(emotionCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([emotion]) => emotion);
      
    const commonTriggers = Object.entries(triggerCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([trigger]) => trigger);

    // Create synthetic mood data for recommendations
    const syntheticMoodData = {
      moodLevel: Math.round(averageMood),
      moodType: recentMoods[0].moodType,
      emotions: commonEmotions,
      triggers: commonTriggers,
      sentimentAnalysis: recentMoods[0].sentimentAnalysis
    };

    const recommendations = await generateRecommendations(syntheticMoodData, recentMoods);

    res.status(200).json({
      success: true,
      recommendations,
      context: {
        averageMood: parseFloat(averageMood.toFixed(1)),
        commonEmotions,
        commonTriggers,
        entriesAnalyzed: recentMoods.length
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Analyze mood trends
// @route   GET /api/ai/trends
// @access  Private
router.get('/trends', async (req, res, next) => {
  try {
    const { period = 'month' } = req.query;
    
    let startDate;
    const endDate = new Date();
    
    switch (period) {
      case 'week':
        startDate = new Date(endDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 1, endDate.getDate());
        break;
      case 'quarter':
        startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 3, endDate.getDate());
        break;
      case 'year':
        startDate = new Date(endDate.getFullYear() - 1, endDate.getMonth(), endDate.getDate());
        break;
      default:
        startDate = new Date(endDate.getFullYear(), endDate.getMonth() - 1, endDate.getDate());
    }

    const moodTrends = await Mood.aggregate([
      {
        $match: {
          user: req.user._id,
          date: { $gte: startDate, $lte: endDate }
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$date' }
          },
          averageMood: { $avg: '$moodLevel' },
          moodCount: { $sum: 1 },
          emotions: { $push: '$emotions' },
          triggers: { $push: '$triggers' }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Calculate trend direction
    let trendDirection = 'stable';
    if (moodTrends.length >= 2) {
      const firstHalf = moodTrends.slice(0, Math.floor(moodTrends.length / 2));
      const secondHalf = moodTrends.slice(Math.floor(moodTrends.length / 2));
      
      const firstHalfAvg = firstHalf.reduce((sum, day) => sum + day.averageMood, 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, day) => sum + day.averageMood, 0) / secondHalf.length;
      
      const difference = secondHalfAvg - firstHalfAvg;
      
      if (difference > 0.5) {
        trendDirection = 'improving';
      } else if (difference < -0.5) {
        trendDirection = 'declining';
      }
    }

    res.status(200).json({
      success: true,
      period,
      startDate,
      endDate,
      trends: moodTrends,
      analysis: {
        trendDirection,
        totalEntries: moodTrends.reduce((sum, day) => sum + day.moodCount, 0),
        averageMoodForPeriod: moodTrends.length > 0 
          ? parseFloat((moodTrends.reduce((sum, day) => sum + day.averageMood, 0) / moodTrends.length).toFixed(1))
          : 0
      }
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get crisis detection and emergency recommendations
// @route   POST /api/ai/crisis-detection
// @access  Private
router.post('/crisis-detection', [
  body('text').optional().isString().withMessage('Text must be a string'),
  body('moodLevel').isInt({ min: 1, max: 10 }).withMessage('Mood level must be between 1 and 10'),
  body('emotions').optional().isArray().withMessage('Emotions must be an array')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { text, moodLevel, emotions = [] } = req.body;
    
    let riskLevel = 'low';
    const riskFactors = [];
    const recommendations = [];

    // Analyze mood level
    if (moodLevel <= 2) {
      riskLevel = 'high';
      riskFactors.push('Very low mood level');
    } else if (moodLevel <= 4) {
      riskLevel = 'medium';
      riskFactors.push('Low mood level');
    }

    // Analyze emotions
    const highRiskEmotions = ['sadness', 'anger', 'fear', 'anxiety'];
    const presentRiskEmotions = emotions.filter(emotion => highRiskEmotions.includes(emotion));
    
    if (presentRiskEmotions.length >= 2) {
      riskLevel = riskLevel === 'low' ? 'medium' : 'high';
      riskFactors.push(`Multiple concerning emotions: ${presentRiskEmotions.join(', ')}`);
    }

    // Analyze text for crisis keywords if provided
    if (text) {
      const crisisKeywords = [
        'suicide', 'kill myself', 'end it all', 'no point', 'hopeless',
        'worthless', 'better off dead', 'can\'t go on', 'give up'
      ];
      
      const textLower = text.toLowerCase();
      const foundCrisisKeywords = crisisKeywords.filter(keyword => textLower.includes(keyword));
      
      if (foundCrisisKeywords.length > 0) {
        riskLevel = 'critical';
        riskFactors.push('Crisis-related language detected');
      }
    }

    // Generate appropriate recommendations
    if (riskLevel === 'critical') {
      recommendations.push({
        type: 'emergency',
        priority: 'immediate',
        title: 'Immediate Support Needed',
        description: 'Please reach out for immediate help',
        actions: [
          'Call emergency services (911) if in immediate danger',
          'Contact National Suicide Prevention Lifeline: 988',
          'Reach out to a trusted friend or family member',
          'Go to the nearest emergency room',
          'Contact your therapist or mental health provider immediately'
        ]
      });
    } else if (riskLevel === 'high') {
      recommendations.push({
        type: 'urgent-support',
        priority: 'high',
        title: 'Urgent Support Recommended',
        description: 'Consider reaching out for professional support',
        actions: [
          'Contact your therapist or mental health provider',
          'Reach out to a trusted friend or family member',
          'Consider calling a mental health helpline',
          'Practice grounding techniques',
          'Avoid being alone if possible'
        ]
      });
    } else if (riskLevel === 'medium') {
      recommendations.push({
        type: 'support',
        priority: 'medium',
        title: 'Additional Support May Help',
        description: 'Consider these supportive actions',
        actions: [
          'Practice self-care activities',
          'Reach out to supportive people in your life',
          'Consider scheduling a therapy session',
          'Use coping strategies you\'ve learned',
          'Monitor your mood closely'
        ]
      });
    }

    res.status(200).json({
      success: true,
      riskLevel,
      riskFactors,
      recommendations,
      emergencyContacts: {
        suicide_prevention: '988',
        crisis_text_line: 'Text HOME to 741741',
        emergency: '911'
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
